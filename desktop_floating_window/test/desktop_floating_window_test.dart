import 'package:flutter_test/flutter_test.dart';
import 'package:desktop_floating_window/desktop_floating_window.dart';
import 'package:desktop_floating_window/desktop_floating_window_platform_interface.dart';
import 'package:desktop_floating_window/desktop_floating_window_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockDesktopFloatingWindowPlatform
    with MockPlatformInterfaceMixin
    implements DesktopFloatingWindowPlatform {

  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final DesktopFloatingWindowPlatform initialPlatform = DesktopFloatingWindowPlatform.instance;

  test('$MethodChannelDesktopFloatingWindow is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelDesktopFloatingWindow>());
  });

  test('getPlatformVersion', () async {
    DesktopFloatingWindow desktopFloatingWindowPlugin = DesktopFloatingWindow();
    MockDesktopFloatingWindowPlatform fakePlatform = MockDesktopFloatingWindowPlatform();
    DesktopFloatingWindowPlatform.instance = fakePlatform;

    expect(await desktopFloatingWindowPlugin.getPlatformVersion(), '42');
  });
}
