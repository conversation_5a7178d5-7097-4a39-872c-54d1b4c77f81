package com.example.desktop_floating_window

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.annotation.NonNull
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

/** DesktopFloatingWindowPlugin */
class DesktopFloatingWindowPlugin : FlutterPlugin, MethodCallHandler, ActivityAware {
  /// The MethodChannel that will the communication between Flutter and native Android
  ///
  /// This local reference serves to register the plugin with the Flutter Engine and unregister it
  /// when the Flutter Engine is detached from the Activity
  private lateinit var channel : MethodChannel
  private lateinit var eventChannel: EventChannel
  private var activity: Activity? = null
  private var context: Context? = null
  
  private val OVERLAY_PERMISSION_REQ_CODE = 1234
  private var pendingResult: Result? = null
  
  // 广播接收器，用于监听悬浮窗点击事件
  private var floatingWindowReceiver: BroadcastReceiver? = null
  private var eventSink: EventChannel.EventSink? = null

  override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    channel = MethodChannel(flutterPluginBinding.binaryMessenger, "desktop_floating_window")
    channel.setMethodCallHandler(this)
    
    eventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "desktop_floating_window_events")
    eventChannel.setStreamHandler(object : EventChannel.StreamHandler {
      override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        eventSink = events
        registerBroadcastReceiver()
      }
      
      override fun onCancel(arguments: Any?) {
        eventSink = null
        unregisterBroadcastReceiver()
      }
    })
    
    context = flutterPluginBinding.applicationContext
  }

  override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
    when (call.method) {
      "checkPermission" -> {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && context != null) {
          result.success(Settings.canDrawOverlays(context))
        } else {
          result.success(true)
        }
      }
      "requestPermission" -> {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && context != null && activity != null) {
          if (Settings.canDrawOverlays(context)) {
            result.success(true)
          } else {
            pendingResult = result
            val intent = Intent(
              Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
              Uri.parse("package:" + activity!!.packageName)
            )
            activity!!.startActivityForResult(intent, OVERLAY_PERMISSION_REQ_CODE)
          }
        } else {
          result.success(true)
        }
      }
      "showFloatingWindow" -> {
        try {
          val serviceIntent = Intent(context, FloatingWindowService::class.java)
          serviceIntent.action = "SHOW_FLOATING_WINDOW"
          context?.startService(serviceIntent)
          result.success(true)
        } catch (e: Exception) {
          result.error("SERVICE_START_ERROR", e.message, null)
        }
      }
      "hideFloatingWindow" -> {
        try {
          val serviceIntent = Intent(context, FloatingWindowService::class.java)
          serviceIntent.action = "HIDE_FLOATING_WINDOW"
          context?.startService(serviceIntent)
          result.success(true)
        } catch (e: Exception) {
          result.error("SERVICE_ERROR", e.message, null)
        }
      }
      "changeFloatingIcon" -> {
        try {
          val iconName = call.argument<String>("iconName") ?: "ic_menu_add"
          val iconResId = context?.resources?.getIdentifier(
            iconName, "drawable", context?.packageName
          ) ?: android.R.drawable.ic_menu_add
          
          val serviceIntent = Intent(context, FloatingWindowService::class.java)
          serviceIntent.action = "CHANGE_ICON"
          serviceIntent.putExtra("icon_res_id", iconResId)
          context?.startService(serviceIntent)
          result.success(true)
        } catch (e: Exception) {
          result.error("CHANGE_ICON_ERROR", e.message, null)
        }
      }
      else -> {
        result.notImplemented()
      }
    }
  }

  private fun registerBroadcastReceiver() {
    if (context != null && floatingWindowReceiver == null) {
      floatingWindowReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
          if (intent?.action == "com.example.desktop_floating_window.FLOATING_WINDOW_CLICKED") {
            eventSink?.success("CLICK")
          }
        }
      }
      
      val filter = IntentFilter("com.example.desktop_floating_window.FLOATING_WINDOW_CLICKED")
      context?.registerReceiver(floatingWindowReceiver, filter)
    }
  }
  
  private fun unregisterBroadcastReceiver() {
    if (context != null && floatingWindowReceiver != null) {
      context?.unregisterReceiver(floatingWindowReceiver)
      floatingWindowReceiver = null
    }
  }

  override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
    channel.setMethodCallHandler(null)
    eventChannel.setStreamHandler(null)
    unregisterBroadcastReceiver()
  }

  override fun onAttachedToActivity(binding: ActivityPluginBinding) {
    activity = binding.activity
    binding.addActivityResultListener { requestCode, resultCode, data ->
      if (requestCode == OVERLAY_PERMISSION_REQ_CODE) {
        pendingResult?.let {
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && context != null) {
            it.success(Settings.canDrawOverlays(context))
          } else {
            it.success(true)
          }
          pendingResult = null
        }
        return@addActivityResultListener true
      }
      return@addActivityResultListener false
    }
  }
  
  override fun onDetachedFromActivityForConfigChanges() {
    activity = null
  }
  
  override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
    activity = binding.activity
  }
  
  override fun onDetachedFromActivity() {
    activity = null
  }
}
