package com.example.desktop_floating_window

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import androidx.core.app.NotificationCompat

class FloatingWindowService : Service() {
    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var params: WindowManager.LayoutParams? = null

    private var initialTouchX: Float = 0f
    private var initialTouchY: Float = 0f
    private var initialX: Int = 0
    private var initialY: Int = 0

    private val NOTIFICATION_ID = 1
    private val CHANNEL_ID = "FloatingWindowChannel"

    override fun onCreate() {
        super.onCreate()
        startForeground()
        createFloatingWindow()
    }

    private fun startForeground() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Floating Window Service",
                NotificationManager.IMPORTANCE_LOW
            )
            channel.lightColor = Color.BLUE
            channel.lockscreenVisibility = Notification.VISIBILITY_PRIVATE

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)

            val notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
                .setOngoing(true)
                .setSmallIcon(android.R.drawable.ic_menu_add)
                .setContentTitle("桌面悬浮窗")
                .setContentText("点击悬浮窗进行操作")
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setCategory(Notification.CATEGORY_SERVICE)

            startForeground(NOTIFICATION_ID, notificationBuilder.build())
        }
    }

    private fun createFloatingWindow() {
        // 初始化WindowManager
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager

        // 创建悬浮窗视图
        floatingView = LayoutInflater.from(this).inflate(R.layout.floating_window_layout, null)

        // 设置窗口参数
        val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            WindowManager.LayoutParams.TYPE_PHONE
        }

        params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            layoutFlag,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        )

        // 初始位置
        params?.gravity = Gravity.TOP or Gravity.START
        params?.x = 0 // 初始位置在左上角
        params?.y = 100

        // 设置拖动处理
        floatingView?.setOnTouchListener(object : View.OnTouchListener {
            private var startX = 0f
            private var startY = 0f
            private var moving = false

            override fun onTouch(view: View, event: MotionEvent): Boolean {
                val screenWidth = windowManager?.defaultDisplay?.width ?: 0

                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        startX = event.rawX
                        startY = event.rawY
                        moving = false
                        return true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        val dx = event.rawX - startX
                        val dy = event.rawY - startY

                        if (!moving && (Math.abs(dx) > 10 || Math.abs(dy) > 10)) {
                            moving = true
                        }

                        if (moving) {
                            // 更新悬浮窗位置
                            params?.x = (params?.x ?: 0) + dx.toInt()
                            params?.y = (params?.y ?: 0) + dy.toInt()

                            // 确保不会超出屏幕
                            params?.x = Math.max(0, Math.min(params?.x ?: 0, screenWidth - (view.width)))

                            windowManager?.updateViewLayout(floatingView, params)

                            // 更新起始点
                            startX = event.rawX
                            startY = event.rawY
                        }
                        return true
                    }
                    MotionEvent.ACTION_UP -> {
                        if (!moving) {
                            // 如果没有移动，视为点击
                            // 发送两个广播：一个给插件内部使用，一个给应用使用
                            val pluginIntent = Intent("com.example.desktop_floating_window.FLOATING_WINDOW_CLICKED")
                            sendBroadcast(pluginIntent)

                            // 发送给应用的广播，触发屏幕捕获
                            val appIntent = Intent("com.example.video_sub_trans.FLOATING_WINDOW_CLICKED")
                            sendBroadcast(appIntent)

                            // 直接触发屏幕捕获
                            val captureIntent = Intent("com.example.video_sub_trans.CAPTURE_SCREEN")
                            sendBroadcast(captureIntent)

                            // 延迟一段时间后，再次发送一个广播，确保屏幕捕获被处理
                            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                                val delayedCaptureIntent = Intent("com.example.video_sub_trans.CAPTURE_SCREEN")
                                sendBroadcast(delayedCaptureIntent)
                            }, 500)
                        } else {
                            // 根据位置判断吸附
                            val centerX = (params?.x ?: 0) + view.width / 2
                            if (centerX < screenWidth / 2) {
                                // 左侧吸附
                                params?.x = 0
                            } else {
                                // 右侧吸附
                                params?.x = screenWidth - view.width
                            }
                            windowManager?.updateViewLayout(floatingView, params)
                        }
                        moving = false
                        return true
                    }
                }
                return false
            }
        })

        // 添加到窗口
        windowManager?.addView(floatingView, params)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (intent?.action == "SHOW_FLOATING_WINDOW") {
            if (floatingView == null) {
                createFloatingWindow()
            } else {
                floatingView?.visibility = View.VISIBLE
            }
        } else if (intent?.action == "HIDE_FLOATING_WINDOW") {
            floatingView?.visibility = View.GONE
        } else if (intent?.action == "CHANGE_ICON") {
            val iconResId = intent?.getIntExtra("icon_res_id", android.R.drawable.ic_menu_add) ?: android.R.drawable.ic_menu_add
            val iconView = floatingView?.findViewById<ImageView>(R.id.floating_icon)
            iconView?.setImageResource(iconResId)
        }
        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        if (floatingView != null) {
            windowManager?.removeView(floatingView)
            floatingView = null
        }
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
}