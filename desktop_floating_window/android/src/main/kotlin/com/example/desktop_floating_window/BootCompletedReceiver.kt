package com.example.desktop_floating_window

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent

class BootCompletedReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED) {
            val serviceIntent = Intent(context, FloatingWindowService::class.java)
            serviceIntent.action = "SHOW_FLOATING_WINDOW"
            context.startService(serviceIntent)
        }
    }
} 