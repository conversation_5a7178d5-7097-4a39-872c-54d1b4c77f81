import 'dart:async';
import 'package:flutter/services.dart';

class DesktopFloatingWindow {
  // 方法通道
  static const MethodChannel _channel = MethodChannel(
    'desktop_floating_window',
  );
  // 事件通道
  static const EventChannel _eventChannel = EventChannel(
    'desktop_floating_window_events',
  );

  // 事件监听器，用于接收悬浮窗的点击事件
  static Stream<String>? _eventStream;

  /// 检查是否有悬浮窗权限
  static Future<bool> checkPermission() async {
    final bool hasPermission = await _channel.invokeMethod('checkPermission');
    return hasPermission;
  }

  /// 请求悬浮窗权限
  static Future<bool> requestPermission() async {
    final bool granted = await _channel.invokeMethod('requestPermission');
    return granted;
  }

  /// 显示悬浮窗
  static Future<bool> showFloatingWindow() async {
    try {
      final bool result = await _channel.invokeMethod('showFloatingWindow');
      return result;
    } catch (e) {
      print('Show floating window error: $e');
      return false;
    }
  }

  /// 隐藏悬浮窗
  static Future<bool> hideFloatingWindow() async {
    try {
      final bool result = await _channel.invokeMethod('hideFloatingWindow');
      return result;
    } catch (e) {
      print('Hide floating window error: $e');
      return false;
    }
  }

  /// 更改悬浮窗图标
  static Future<bool> changeFloatingIcon(String iconName) async {
    try {
      final bool result = await _channel.invokeMethod('changeFloatingIcon', {
        'iconName': iconName,
      });
      return result;
    } catch (e) {
      print('Change floating icon error: $e');
      return false;
    }
  }

  /// 监听悬浮窗点击事件
  static Stream<String> get onFloatingWindowClick {
    _eventStream ??= _eventChannel.receiveBroadcastStream().map(
      (event) => event.toString(),
    );
    return _eventStream!;
  }
}
