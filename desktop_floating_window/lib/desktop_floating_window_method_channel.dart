import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'desktop_floating_window_platform_interface.dart';

/// An implementation of [DesktopFloatingWindowPlatform] that uses method channels.
class MethodChannelDesktopFloatingWindow extends DesktopFloatingWindowPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('desktop_floating_window');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
