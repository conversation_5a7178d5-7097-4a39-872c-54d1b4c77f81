import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'desktop_floating_window_method_channel.dart';

abstract class DesktopFloatingWindowPlatform extends PlatformInterface {
  /// Constructs a DesktopFloatingWindowPlatform.
  DesktopFloatingWindowPlatform() : super(token: _token);

  static final Object _token = Object();

  static DesktopFloatingWindowPlatform _instance = MethodChannelDesktopFloatingWindow();

  /// The default instance of [DesktopFloatingWindowPlatform] to use.
  ///
  /// Defaults to [MethodChannelDesktopFloatingWindow].
  static DesktopFloatingWindowPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [DesktopFloatingWindowPlatform] when
  /// they register themselves.
  static set instance(DesktopFloatingWindowPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
