<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000"
    android:fitsSystemWindows="true">

    <LinearLayout
        android:id="@+id/translation_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="16dp"
        android:fitsSystemWindows="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="#F2FFFFFF"
            android:padding="20dp"
            android:layout_margin="16dp"
            android:elevation="8dp">

            <TextView
                android:id="@+id/original_text_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="识别文本："
                android:textStyle="bold"
                android:textColor="#666666"
                android:textSize="16sp"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/original_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#000000"
                android:textSize="16sp"
                android:layout_marginBottom="20dp"
                android:text="识别的文本将显示在这里" />

            <TextView
                android:id="@+id/translation_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="识别结果："
                android:textStyle="bold"
                android:textColor="#0066CC"
                android:textSize="16sp"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/translation_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#000000"
                android:textSize="18sp"
                android:textStyle="bold"
                android:text="识别结果将显示在这里" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end"
                android:layout_marginTop="24dp">

                <Button
                    android:id="@+id/copy_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="复制"
                    android:textSize="16sp"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:layout_marginEnd="12dp" />

                <Button
                    android:id="@+id/close_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="关闭"
                    android:textSize="16sp"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp" />

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>
