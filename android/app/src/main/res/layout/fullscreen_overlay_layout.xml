<?xml version="1.0" encoding="utf-8"?>
<!-- 半透明全屏悬浮窗，用于显示所有识别的文本内容 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000"
    android:fitsSystemWindows="true">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <!-- 标题栏 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="#E0FF0000"
                android:padding="12dp"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="屏幕文本识别结果"
                    android:textColor="#FFFFFF"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:gravity="center" />

                <Button
                    android:id="@+id/close_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="关闭"
                    android:textSize="14sp"
                    android:textColor="#FFFFFF"
                    android:background="@android:color/transparent"
                    android:padding="8dp" />

            </LinearLayout>

            <!-- 文本内容区域 -->
            <TextView
                android:id="@+id/recognized_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#E0FF0000"
                android:padding="16dp"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:lineSpacingExtra="4dp"
                android:gravity="start"
                android:text="识别的文本内容将显示在这里..."
                android:selectAllOnFocus="true"
                android:textIsSelectable="true" />

            <!-- 操作按钮区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginTop="16dp">

                <Button
                    android:id="@+id/copy_all_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="复制全部"
                    android:textSize="16sp"
                    android:textColor="#FFFFFF"
                    android:background="#E0FF0000"
                    android:padding="12dp"
                    android:layout_marginEnd="16dp" />

                <Button
                    android:id="@+id/clear_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="清除"
                    android:textSize="16sp"
                    android:textColor="#FFFFFF"
                    android:background="#E0666666"
                    android:padding="12dp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</FrameLayout>
