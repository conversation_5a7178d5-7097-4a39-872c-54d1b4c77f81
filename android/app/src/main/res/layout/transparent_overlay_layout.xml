<?xml version="1.0" encoding="utf-8"?>
<!-- 半透明红色背景的文本识别结果悬浮窗 -->
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/overlay_text"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="#80FF0000"
    android:padding="6dp"
    android:textColor="#FFFFFF"
    android:textSize="16sp"
    android:textStyle="bold"
    android:shadowColor="#000000"
    android:shadowDx="2"
    android:shadowDy="2"
    android:shadowRadius="3"
    android:visibility="visible"
    android:maxLines="3"
    android:ellipsize="end"
    android:gravity="center"
    android:text="识别结果将显示在这里" />
