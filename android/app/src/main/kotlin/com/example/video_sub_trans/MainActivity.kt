package com.example.video_sub_trans

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.projection.MediaProjectionManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant
import com.example.video_sub_trans.utils.ScreenCaptureUtils
import com.example.video_sub_trans.services.ScreenCaptureService
import com.example.video_sub_trans.services.FloatingWindowService

class MainActivity : FlutterActivity() {
    private val TAG = "MainActivity"
    private val FLOATING_WINDOW_CHANNEL = "app/floating_window"
    private val SCREEN_CAPTURE_CHANNEL = "app/screen_capture"
    private val REQUEST_CODE_OVERLAY_PERMISSION = 1001
    private val REQUEST_CODE_MEDIA_PROJECTION = 1002

    private var screenCaptureIntent: Intent? = null
    private var screenCaptureResultCode: Int = 0
    private lateinit var screenCaptureUtils: ScreenCaptureUtils
    private var isFloatingWindowVisible = false

    // BroadcastReceiver for floating window clicks
    private val floatingWindowReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            Log.d(TAG, "Received broadcast: ${intent.action}")
            if (intent.action == "com.example.video_sub_trans.FLOATING_WINDOW_CLICKED") {
                // 通知Flutter悬浮窗被点击
                flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                    MethodChannel(messenger, FLOATING_WINDOW_CHANNEL)
                        .invokeMethod("onWindowClick", null)
                }
            } else if (intent.action == "com.example.video_sub_trans.CAPTURE_SCREEN") {
                // 启动屏幕捕获
                Log.d(TAG, "Triggering screen capture from broadcast")
                if (screenCaptureIntent != null && screenCaptureResultCode != 0) {
                    // 直接执行屏幕捕获
                    try {
                        Log.d(TAG, "Executing screen capture directly")

                        // 确保ScreenCaptureUtils已初始化
                        if (!::screenCaptureUtils.isInitialized) {
                            screenCaptureUtils = ScreenCaptureUtils(this@MainActivity)
                        }

                        // 重新初始化屏幕捕获，确保使用最新的权限
                        screenCaptureUtils.init(screenCaptureResultCode, screenCaptureIntent!!)

                        // 捕获屏幕并识别文本，获取位置信息
                        screenCaptureUtils.captureAndRecognizeWithBounds { textBounds, error ->
                            if (error != null) {
                                Log.e(TAG, "Error recognizing text with bounds", error)

                                // 如果是ImageReader错误，尝试重新初始化并重试一次
                                if (error.message?.contains("ImageReader") == true) {
                                    Log.d(TAG, "检测到ImageReader错误，尝试重新初始化并重试")

                                    // 延迟一段时间后重试
                                    Handler(Looper.getMainLooper()).postDelayed({
                                        try {
                                            // 重新初始化
                                            screenCaptureUtils = ScreenCaptureUtils(this@MainActivity)
                                            screenCaptureUtils.init(screenCaptureResultCode, screenCaptureIntent!!)

                                            // 重新捕获
                                            screenCaptureUtils.captureAndRecognizeWithBounds { retryTextBounds, retryError ->
                                                processRecognizedTextBounds(retryTextBounds, retryError)
                                            }
                                        } catch (e: Exception) {
                                            Log.e(TAG, "重试过程中出错", e)
                                        }
                                    }, 1000) // 延迟1秒后重试
                                }
                            } else {
                                processRecognizedTextBounds(textBounds, error)
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error executing screen capture", e)
                    }

                    // 同时通知Flutter执行捕获
                    flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                        MethodChannel(messenger, SCREEN_CAPTURE_CHANNEL)
                            .invokeMethod("onCaptureTriggered", null)
                    }
                } else {
                    Log.e(TAG, "Screen capture not initialized")
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化屏幕捕获工具
        screenCaptureUtils = ScreenCaptureUtils(this)

        // 注册广播接收器
        val filter = IntentFilter().apply {
            addAction("com.example.video_sub_trans.FLOATING_WINDOW_CLICKED")
            addAction("com.example.video_sub_trans.CAPTURE_SCREEN")
        }
        registerReceiver(floatingWindowReceiver, filter)

        Log.d(TAG, "MainActivity created, broadcast receiver registered")
    }

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // 注册翻译通道
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "app/translation").setMethodCallHandler { call, result ->
            when (call.method) {
                "translateText" -> {
                    val text = call.arguments as String
                    // 简单翻译，实际应用中应该调用真正的翻译API
                    result.success("Translated: $text")
                }
                else -> result.notImplemented()
            }
        }

        // 注册浮动窗口通道
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, FLOATING_WINDOW_CHANNEL).setMethodCallHandler { call, result ->
            Log.d(TAG, "Received method call: ${call.method}")
            when (call.method) {
                "checkPermission" -> {
                    val hasPermission = checkOverlayPermission()
                    Log.d(TAG, "Check permission result: $hasPermission")
                    result.success(hasPermission)
                }
                "requestPermission" -> {
                    Log.d(TAG, "Requesting overlay permission")
                    requestOverlayPermission()
                    result.success(true)
                }
                "show" -> {
                    Log.d(TAG, "Showing floating window")
                    showFloatingWindow()
                    result.success(true)
                }
                "hide" -> {
                    Log.d(TAG, "Hiding floating window")
                    hideFloatingWindow()
                    result.success(true)
                }
                "isVisible" -> {
                    Log.d(TAG, "Checking if floating window is visible: $isFloatingWindowVisible")
                    result.success(isFloatingWindowVisible)
                }
                else -> result.notImplemented()
            }
        }

        // 注册屏幕捕获通道
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SCREEN_CAPTURE_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "hasPermission" -> result.success(screenCaptureIntent != null)
                "requestPermission" -> {
                    requestScreenCapturePermission()
                    result.success(true)
                }
                "translateText" -> {
                    val text = call.arguments as String
                    // 调用Flutter端的翻译方法
                    flutterEngine.dartExecutor.binaryMessenger.let { messenger ->
                        MethodChannel(messenger, "app/translation")
                            .invokeMethod("translateText", text, object : MethodChannel.Result {
                                override fun success(translatedText: Any?) {
                                    result.success(translatedText)
                                }

                                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                                    result.error(errorCode, errorMessage, errorDetails)
                                }

                                override fun notImplemented() {
                                    // 如果Flutter端没有实现翻译方法，使用简单的翻译
                                    result.success("Translated: $text")
                                }
                            })
                    }
                }
                "forceCaptureScreen" -> {
                    if (screenCaptureIntent != null && screenCaptureResultCode != 0) {
                        captureAndTranslateScreen(result)
                    } else {
                        Log.e(TAG, "Screen capture not initialized")
                        result.error("NOT_INITIALIZED", "Screen capture not initialized", null)
                    }
                }
                "captureScreenWithBounds" -> {
                    if (screenCaptureIntent != null && screenCaptureResultCode != 0) {
                        captureAndTranslateScreenWithBounds(result)
                    } else {
                        Log.e(TAG, "Screen capture not initialized")
                        result.error("NOT_INITIALIZED", "Screen capture not initialized", null)
                    }
                }
                "showTranslationOverlay" -> {
                    try {
                        val originalText = call.argument<String>("originalText") ?: "无原文"
                        val translatedText = call.argument<String>("translatedText") ?: "无翻译结果"
                        val x = call.argument<Int>("x") ?: 0
                        val y = call.argument<Int>("y") ?: 0
                        val width = call.argument<Int>("width") ?: 300
                        val height = call.argument<Int>("height") ?: 100

                        showTranslationOverlay(originalText, translatedText, x, y, width, height)
                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error showing translation overlay", e)
                        result.error("OVERLAY_ERROR", "Error showing translation overlay: ${e.message}", null)
                    }
                }
                "startService" -> {
                    if (screenCaptureIntent != null) {
                        startScreenCaptureService()
                        result.success(true)
                    } else {
                        result.error("NO_PERMISSION", "Screen capture permission not granted", null)
                    }
                }
                "stopService" -> {
                    stopScreenCaptureService()
                    result.success(true)
                }
                "saveDebugLogs" -> {
                    val logs = call.argument<List<String>>("logs")
                    logs?.let {
                        // 日志保存到本地文件
                        Log.d(TAG, "Saving ${it.size} debug logs")
                    }
                    result.success(true)
                }
                else -> result.notImplemented()
            }
        }
    }

    // 检查悬浮窗权限
    private fun checkOverlayPermission(): Boolean {
        val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(this)
        } else {
            true // 低版本Android默认允许
        }
        Log.d(TAG, "Overlay permission check: $hasPermission")
        return hasPermission
    }

    // 请求悬浮窗权限
    private fun requestOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
            val intent = Intent(
                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                Uri.parse("package:$packageName")
            )
            Log.d(TAG, "Starting activity for overlay permission")
            startActivityForResult(intent, REQUEST_CODE_OVERLAY_PERMISSION)
        } else {
            Log.d(TAG, "Overlay permission already granted or not needed")
        }
    }

    // 显示悬浮窗
    private fun showFloatingWindow() {
        if (checkOverlayPermission()) {
            Log.d(TAG, "Showing floating window")
            val intent = Intent(this, FloatingWindowService::class.java)
            intent.action = FloatingWindowService.ACTION_SHOW
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
            isFloatingWindowVisible = true
        } else {
            Log.e(TAG, "Can't show floating window: no permission")
            requestOverlayPermission()
        }
    }

    // 隐藏悬浮窗
    private fun hideFloatingWindow() {
        Log.d(TAG, "Hiding floating window")
        val intent = Intent(this, FloatingWindowService::class.java)
        intent.action = FloatingWindowService.ACTION_HIDE
        startService(intent)
        isFloatingWindowVisible = false
    }

    // 请求屏幕捕获权限
    private fun requestScreenCapturePermission() {
        val mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        val intent = mediaProjectionManager.createScreenCaptureIntent()
        startActivityForResult(intent, REQUEST_CODE_MEDIA_PROJECTION)
    }

    // 捕获屏幕并进行文本识别
    private fun captureAndTranslateScreen(result: MethodChannel.Result) {
        screenCaptureIntent?.let { intent ->
            try {
                // 确保ScreenCaptureUtils已初始化
                if (!::screenCaptureUtils.isInitialized) {
                    screenCaptureUtils = ScreenCaptureUtils(this)
                }

                // 重新初始化屏幕捕获，确保使用最新的权限
                screenCaptureUtils.init(screenCaptureResultCode, intent)

                // 添加日志，帮助调试
                Log.d(TAG, "Starting screen capture and text recognition")

                // 捕获屏幕并识别文本
                screenCaptureUtils.captureAndRecognize { text, error ->
                    if (error != null) {
                        Log.e(TAG, "Error recognizing text", error)

                        // 如果是ImageReader错误，尝试重新初始化并重试一次
                        if (error.message?.contains("ImageReader") == true) {
                            Log.d(TAG, "检测到ImageReader错误，尝试重新初始化并重试")

                            // 延迟一段时间后重试
                            Handler(Looper.getMainLooper()).postDelayed({
                                try {
                                    // 重新初始化
                                    screenCaptureUtils = ScreenCaptureUtils(this)
                                    screenCaptureUtils.init(screenCaptureResultCode, intent)

                                    // 重新捕获
                                    screenCaptureUtils.captureAndRecognize { retryText, retryError ->
                                        if (retryError != null) {
                                            Log.e(TAG, "重试后仍然出错", retryError)
                                            result.error("OCR_ERROR", "Error recognizing text after retry: ${retryError.message}", null)
                                        } else if (retryText != null) {
                                            Log.d(TAG, "重试成功，识别到文本: ${if (retryText.length > 50) retryText.substring(0, 50) + "..." else retryText}")
                                            result.success(retryText)
                                        } else {
                                            Log.d(TAG, "重试后未识别到文本")
                                            result.error("NO_TEXT", "No text recognized after retry", null)
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "重试过程中出错", e)
                                    result.error("RETRY_ERROR", "Error during retry: ${e.message}", null)
                                }
                            }, 1000) // 延迟1秒后重试
                        } else {
                            // 其他错误直接返回
                            result.error("OCR_ERROR", "Error recognizing text: ${error.message}", null)
                        }
                    } else if (text != null) {
                        Log.d(TAG, "Text recognized: ${if (text.length > 50) text.substring(0, 50) + "..." else text}")
                        // 返回识别的文本到Flutter
                        result.success(text)
                    } else {
                        Log.d(TAG, "No text recognized")
                        result.error("NO_TEXT", "No text recognized", null)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error capturing screen", e)
                result.error("CAPTURE_ERROR", "Error capturing screen: ${e.message}", null)
            }
        } ?: run {
            Log.e(TAG, "Screen capture permission not granted")
            result.error("NO_PERMISSION", "Screen capture permission not granted", null)
        }
    }

    // 启动屏幕捕获服务
    private fun startScreenCaptureService() {
        val serviceIntent = Intent(this, ScreenCaptureService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(serviceIntent)
        } else {
            startService(serviceIntent)
        }
        Log.d(TAG, "Screen capture service started")
    }

    // 停止屏幕捕获服务
    private fun stopScreenCaptureService() {
        val serviceIntent = Intent(this, ScreenCaptureService::class.java)
        stopService(serviceIntent)
        Log.d(TAG, "Screen capture service stopped")
    }

    // 捕获屏幕并进行文本识别，返回文本和位置信息
    private fun captureAndTranslateScreenWithBounds(result: MethodChannel.Result) {
        screenCaptureIntent?.let { intent ->
            try {
                // 确保ScreenCaptureUtils已初始化
                if (!::screenCaptureUtils.isInitialized) {
                    screenCaptureUtils = ScreenCaptureUtils(this)
                }

                // 重新初始化屏幕捕获，确保使用最新的权限
                screenCaptureUtils.init(screenCaptureResultCode, intent)

                // 添加日志，帮助调试
                Log.d(TAG, "Starting screen capture and text recognition with bounds")

                // 捕获屏幕并识别文本，获取位置信息
                screenCaptureUtils.captureAndRecognizeWithBounds { textBounds, error ->
                    if (error != null) {
                        Log.e(TAG, "Error recognizing text with bounds", error)

                        // 如果是ImageReader错误，尝试重新初始化并重试一次
                        if (error.message?.contains("ImageReader") == true) {
                            Log.d(TAG, "检测到ImageReader错误，尝试重新初始化并重试")

                            // 延迟一段时间后重试
                            Handler(Looper.getMainLooper()).postDelayed({
                                try {
                                    // 重新初始化
                                    screenCaptureUtils = ScreenCaptureUtils(this)
                                    screenCaptureUtils.init(screenCaptureResultCode, intent)

                                    // 重新捕获
                                    screenCaptureUtils.captureAndRecognizeWithBounds { retryTextBounds, retryError ->
                                        if (retryError != null) {
                                            Log.e(TAG, "重试后仍然出错", retryError)
                                            result.error("OCR_ERROR", "Error recognizing text with bounds after retry: ${retryError.message}", null)
                                        } else if (retryTextBounds != null && retryTextBounds.isNotEmpty()) {
                                            Log.d(TAG, "重试成功，识别到文本块: ${retryTextBounds.size}")

                                            // 将Map<String, List<Float>>转换为Flutter可以理解的格式
                                            val resultMap = HashMap<String, Any>()
                                            retryTextBounds.forEach { (text, bounds) ->
                                                resultMap[text] = bounds
                                            }

                                            // 返回识别的文本和位置信息到Flutter
                                            result.success(resultMap)
                                        } else {
                                            Log.d(TAG, "重试后未识别到文本")
                                            result.error("NO_TEXT", "No text recognized with bounds after retry", null)
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "重试过程中出错", e)
                                    result.error("RETRY_ERROR", "Error during retry: ${e.message}", null)
                                }
                            }, 1000) // 延迟1秒后重试
                        } else {
                            // 其他错误直接返回
                            result.error("OCR_ERROR", "Error recognizing text with bounds: ${error.message}", null)
                        }
                    } else if (textBounds != null && textBounds.isNotEmpty()) {
                        Log.d(TAG, "Text recognized with bounds: ${textBounds.size} blocks")

                        // 将Map<String, List<Float>>转换为Flutter可以理解的格式
                        val resultMap = HashMap<String, Any>()
                        textBounds.forEach { (text, bounds) ->
                            resultMap[text] = bounds
                        }

                        // 返回识别的文本和位置信息到Flutter
                        result.success(resultMap)
                    } else {
                        Log.d(TAG, "No text recognized with bounds")
                        result.error("NO_TEXT", "No text recognized with bounds", null)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error capturing screen with bounds", e)
                result.error("CAPTURE_ERROR", "Error capturing screen with bounds: ${e.message}", null)
            }
        } ?: run {
            Log.e(TAG, "Screen capture permission not granted")
            result.error("NO_PERMISSION", "Screen capture permission not granted", null)
        }
    }

    // 处理识别的文本和位置信息
    private fun processRecognizedTextBounds(textBounds: Map<String, List<Float>>?, error: Exception?) {
        if (error != null) {
            Log.e(TAG, "Error processing recognized text bounds", error)
        } else if (textBounds != null && textBounds.isNotEmpty()) {
            Log.d(TAG, "Processing recognized text bounds: ${textBounds.size} blocks")

            // 选择第一个文本块进行翻译
            val firstEntry = textBounds.entries.firstOrNull()
            if (firstEntry != null) {
                val originalText = firstEntry.key
                val bounds = firstEntry.value

                // 直接显示识别的文本，不进行翻译
                val x = bounds[0].toInt()
                val y = bounds[1].toInt()
                val width = (bounds[2] - bounds[0]).toInt()
                val height = (bounds[3] - bounds[1]).toInt()

                // 直接使用识别的文本作为"翻译结果"
                Log.d(TAG, "准备显示识别文本，原始内容: $originalText")
                Log.d(TAG, "文本位置: x=$x, y=$y, width=$width, height=$height")

                // 确保文本不为空
                val textToShow = if (originalText.isBlank()) "无法识别文本" else originalText

                // 显示翻译结果
                showTranslationOverlay("识别文本", textToShow, x, y, width, height)

                Log.d(TAG, "直接显示识别的文本: $originalText 在位置 x=$x, y=$y, width=$width, height=$height")
            } else {
                Log.d(TAG, "No text blocks found")
            }
        } else {
            Log.d(TAG, "No text recognized with bounds")
        }
    }

    // 透明悬浮窗显示翻译结果
    private fun showTranslationOverlay(originalText: String, translatedText: String, x: Int, y: Int, width: Int, height: Int) {
        try {
            // 确保悬浮窗服务已启动
            if (!isFloatingWindowVisible) {
                Log.d(TAG, "悬浮窗服务未启动，先启动服务")
                showFloatingWindow()
            }

            // 创建Intent，发送到FloatingWindowService
            val intent = Intent(this, FloatingWindowService::class.java).apply {
                action = FloatingWindowService.ACTION_SHOW_TRANSLATION
                putExtra("originalText", originalText)
                putExtra("translatedText", translatedText)
                putExtra("x", x)
                putExtra("y", y)
                putExtra("width", width)
                putExtra("height", height)
            }

            Log.d(TAG, "准备发送显示翻译结果命令，位置: x=$x, y=$y, width=$width, height=$height")

            // 启动或更新服务
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Log.d(TAG, "使用startForegroundService启动服务")
                startForegroundService(intent)
            } else {
                Log.d(TAG, "使用startService启动服务")
                startService(intent)
            }

            Log.d(TAG, "发送显示翻译结果命令到悬浮窗服务: originalText=${if (originalText.length > 50) originalText.substring(0, 50) + "..." else originalText}, translatedText=${if (translatedText.length > 50) translatedText.substring(0, 50) + "..." else translatedText}")
        } catch (e: Exception) {
            Log.e(TAG, "显示翻译结果失败", e)
            throw e
        }
    }

    // 停止悬浮窗服务
    private fun stopFloatingWindowService() {
        val intent = Intent(this, FloatingWindowService::class.java)
        stopService(intent)
        isFloatingWindowVisible = false
        Log.d(TAG, "Floating window service stopped")
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            REQUEST_CODE_OVERLAY_PERMISSION -> {
                Log.d(TAG, "Overlay permission result received")
                // 通知Flutter端权限状态变更
                flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                    MethodChannel(messenger, FLOATING_WINDOW_CHANNEL)
                        .invokeMethod("onPermissionResult", checkOverlayPermission())
                }
            }
            REQUEST_CODE_MEDIA_PROJECTION -> {
                if (resultCode == Activity.RESULT_OK && data != null) {
                    screenCaptureIntent = data
                    screenCaptureResultCode = resultCode
                    Log.d(TAG, "Media projection permission granted")

                    // 通知Flutter端权限状态变更
                    flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                        MethodChannel(messenger, SCREEN_CAPTURE_CHANNEL)
                            .invokeMethod("onPermissionResult", true)
                    }
                } else {
                    Log.d(TAG, "Media projection permission denied")
                    // 通知Flutter端权限状态变更
                    flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                        MethodChannel(messenger, SCREEN_CAPTURE_CHANNEL)
                            .invokeMethod("onPermissionResult", false)
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        // 注销广播接收器
        unregisterReceiver(floatingWindowReceiver)

        if (::screenCaptureUtils.isInitialized) {
            screenCaptureUtils.release()
        }
        stopScreenCaptureService()
        stopFloatingWindowService()
        super.onDestroy()
    }
}
