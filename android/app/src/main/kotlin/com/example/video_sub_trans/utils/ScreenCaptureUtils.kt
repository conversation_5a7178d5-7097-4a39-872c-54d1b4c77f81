package com.example.video_sub_trans.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.WindowManager
import com.example.video_sub_trans.ml.TextRecognizer
import java.nio.ByteBuffer

/**
 * 屏幕捕获工具类，用于捕获屏幕内容并进行OCR文本识别
 */
class ScreenCaptureUtils(private val context: Context) {

    private val TAG = "ScreenCaptureUtils"

    private var mediaProjection: MediaProjection? = null
    private var virtualDisplay: VirtualDisplay? = null
    private var imageReader: ImageReader? = null
    private var textRecognizer: TextRecognizer? = null

    private var screenWidth = 0
    private var screenHeight = 0
    private var screenDensity = 0

    /**
     * 初始化屏幕捕获
     */
    fun init(resultCode: Int, data: Intent) {
        // 获取屏幕参数
        val metrics = DisplayMetrics()
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        windowManager.defaultDisplay.getMetrics(metrics)

        screenWidth = metrics.widthPixels
        screenHeight = metrics.heightPixels
        screenDensity = metrics.densityDpi

        // 初始化媒体投影
        val projectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        mediaProjection = projectionManager.getMediaProjection(resultCode, data)

        // 创建OCR识别器
        textRecognizer = TextRecognizer(context)

        Log.d(TAG, "Screen capture initialized: ${screenWidth}x${screenHeight}@${screenDensity}dpi")
    }

    /**
     * 捕获屏幕并识别文本
     */
    fun captureAndRecognize(preferChinese: Boolean = true, callback: (String?, Exception?) -> Unit) {
        if (mediaProjection == null) {
            Log.e(TAG, "Media projection not initialized")
            callback(null, IllegalStateException("Media projection not initialized"))
            return
        }

        try {
            // 确保先释放之前的资源
            releaseVirtualDisplay()

            // 创建ImageReader用于接收屏幕图像
            imageReader = ImageReader.newInstance(
                screenWidth, screenHeight,
                PixelFormat.RGBA_8888, 2
            )

            // 创建虚拟显示
            virtualDisplay = mediaProjection?.createVirtualDisplay(
                "ScreenCapture",
                screenWidth, screenHeight, screenDensity,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader?.surface, null, null
            )

            // 标记ImageReader正在使用中
            isUsingImageReader = true

            // 延迟更长时间，确保屏幕内容已经完全渲染到ImageReader中
            Log.d(TAG, "等待屏幕内容渲染到ImageReader...")
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    val reader = imageReader
                    if (reader != null && reader.surface.isValid) {
                        Log.d(TAG, "ImageReader有效，尝试获取图像")
                        val image = reader.acquireLatestImage()
                        if (image != null) {
                            Log.d(TAG, "成功获取屏幕图像")
                            try {
                                val bitmap = imageToBitmap(image)
                                Log.d(TAG, "成功将图像转换为Bitmap，开始OCR识别")

                                // 进行OCR识别
                                textRecognizer?.recognizeText(bitmap, preferChinese) { text, error ->
                                    // 标记ImageReader不再使用
                                    isUsingImageReader = false
                                    Log.d(TAG, "OCR识别完成，释放资源")
                                    releaseVirtualDisplay()
                                    callback(text, error)
                                }
                            } finally {
                                // 确保Image被关闭
                                image.close()
                                Log.d(TAG, "图像已关闭")
                            }
                        } else {
                            Log.e(TAG, "Failed to acquire screen image")
                            // 标记ImageReader不再使用
                            isUsingImageReader = false
                            releaseVirtualDisplay()
                            callback(null, Exception("Failed to acquire screen image"))
                        }
                    } else {
                        Log.e(TAG, "ImageReader is null or surface is invalid")
                        // 标记ImageReader不再使用
                        isUsingImageReader = false
                        releaseVirtualDisplay()
                        callback(null, Exception("ImageReader is null or surface is invalid"))
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error acquiring image", e)
                    // 标记ImageReader不再使用
                    isUsingImageReader = false
                    releaseVirtualDisplay()
                    callback(null, e)
                }
            }, 800) // 增加延迟时间到800毫秒

        } catch (e: Exception) {
            Log.e(TAG, "Error capturing screen", e)
            releaseVirtualDisplay()
            callback(null, e)
        }
    }

    /**
     * 将Image转换为Bitmap
     */
    private fun imageToBitmap(image: Image): Bitmap {
        val width = image.width
        val height = image.height
        val planes = image.planes
        val buffer = planes[0].buffer
        val pixelStride = planes[0].pixelStride
        val rowStride = planes[0].rowStride
        val rowPadding = rowStride - pixelStride * width

        // 创建Bitmap
        val bitmap = Bitmap.createBitmap(
            width + rowPadding / pixelStride,
            height, Bitmap.Config.ARGB_8888
        )
        buffer.rewind()
        bitmap.copyPixelsFromBuffer(buffer)

        // 裁剪到实际屏幕大小
        return Bitmap.createBitmap(bitmap, 0, 0, width, height)
    }

    /**
     * 释放虚拟显示资源
     */
    // 标记是否正在使用ImageReader
    private var isUsingImageReader = false

    // 释放虚拟显示资源，但保留ImageReader
    private fun releaseVirtualDisplay() {
        try {
            // 先释放虚拟显示
            virtualDisplay?.release()
            virtualDisplay = null

            // 如果不在使用中，才关闭ImageReader
            if (!isUsingImageReader) {
                closeImageReader()
            } else {
                Log.d(TAG, "ImageReader正在使用中，暂不关闭")
            }

            // 强制GC，帮助释放资源
            System.gc()
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing virtual display", e)
        }
    }

    // 关闭ImageReader的单独方法
    private fun closeImageReader() {
        try {
            // 确保在关闭ImageReader之前，所有相关的Image对象都已关闭
            imageReader?.setOnImageAvailableListener(null, null)
            imageReader?.close()
            imageReader = null
            Log.d(TAG, "ImageReader已关闭并设为null")
        } catch (e: Exception) {
            Log.e(TAG, "关闭ImageReader失败", e)
        }
    }

    /**
     * 捕获屏幕并识别文本，返回文本和位置信息
     */
    fun captureAndRecognizeWithBounds(preferChinese: Boolean = true, callback: (Map<String, List<Float>>?, Exception?) -> Unit) {
        if (mediaProjection == null) {
            Log.e(TAG, "Media projection not initialized")
            callback(null, IllegalStateException("Media projection not initialized"))
            return
        }

        try {
            // 确保先释放之前的资源
            releaseVirtualDisplay()

            // 创建ImageReader用于接收屏幕图像
            imageReader = ImageReader.newInstance(
                screenWidth, screenHeight,
                PixelFormat.RGBA_8888, 2
            )

            // 创建虚拟显示
            virtualDisplay = mediaProjection?.createVirtualDisplay(
                "ScreenCapture",
                screenWidth, screenHeight, screenDensity,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader?.surface, null, null
            )

            // 标记ImageReader正在使用中
            isUsingImageReader = true

            // 延迟更长时间，确保屏幕内容已经完全渲染到ImageReader中
            Log.d(TAG, "等待屏幕内容渲染到ImageReader...")
            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    val reader = imageReader
                    if (reader != null && reader.surface.isValid) {
                        Log.d(TAG, "ImageReader有效，尝试获取图像")
                        val image = reader.acquireLatestImage()
                        if (image != null) {
                            Log.d(TAG, "成功获取屏幕图像")
                            try {
                                val bitmap = imageToBitmap(image)
                                Log.d(TAG, "成功将图像转换为Bitmap，开始OCR识别")

                                // 进行OCR识别，并获取位置信息
                                textRecognizer?.recognizeTextWithBounds(bitmap, preferChinese) { textBounds, error ->
                                    // 标记ImageReader不再使用
                                    isUsingImageReader = false
                                    Log.d(TAG, "OCR识别完成，释放资源")
                                    releaseVirtualDisplay()
                                    callback(textBounds, error)
                                }
                            } finally {
                                // 确保Image被关闭
                                image.close()
                                Log.d(TAG, "图像已关闭")
                            }
                        } else {
                            Log.e(TAG, "Failed to acquire screen image")
                            // 标记ImageReader不再使用
                            isUsingImageReader = false
                            releaseVirtualDisplay()
                            callback(null, Exception("Failed to acquire screen image"))
                        }
                    } else {
                        Log.e(TAG, "ImageReader is null or surface is invalid")
                        // 标记ImageReader不再使用
                        isUsingImageReader = false
                        releaseVirtualDisplay()
                        callback(null, Exception("ImageReader is null or surface is invalid"))
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error acquiring image", e)
                    // 标记ImageReader不再使用
                    isUsingImageReader = false
                    releaseVirtualDisplay()
                    callback(null, e)
                }
            }, 800) // 增加延迟时间到800毫秒

        } catch (e: Exception) {
            Log.e(TAG, "Error capturing screen", e)
            releaseVirtualDisplay()
            callback(null, e)
        }
    }

    /**
     * 释放所有资源
     */
    fun release() {
        releaseVirtualDisplay()
        mediaProjection?.stop()
        mediaProjection = null
        textRecognizer?.close()
        textRecognizer = null
    }
}