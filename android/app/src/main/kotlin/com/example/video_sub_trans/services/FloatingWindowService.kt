package com.example.video_sub_trans.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import android.widget.LinearLayout
import android.widget.FrameLayout
import android.widget.Toast
import androidx.core.app.NotificationCompat
import com.example.video_sub_trans.R

class FloatingWindowService : Service() {
    private val TAG = "FloatingWindowService"

    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var params: WindowManager.LayoutParams? = null

    // 翻译结果悬浮窗 - 支持多个悬浮窗
    private val translationOverlayViews = mutableMapOf<Int, View>()
    private val translationParams = mutableMapOf<Int, WindowManager.LayoutParams>()

    private var initialTouchX: Float = 0f
    private var initialTouchY: Float = 0f
    private var initialX: Int = 0
    private var initialY: Int = 0

    private val NOTIFICATION_ID = 2
    private val CHANNEL_ID = "FloatingWindowChannel"

    companion object {
        const val ACTION_SHOW = "SHOW_FLOATING_WINDOW"
        const val ACTION_HIDE = "HIDE_FLOATING_WINDOW"
        const val ACTION_SHOW_TRANSLATION = "SHOW_TRANSLATION_OVERLAY"
        const val ACTION_CLEAR_ALL_TRANSLATIONS = "CLEAR_ALL_TRANSLATIONS"
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate 创建悬浮窗服务")
        startForeground()
    }

    private fun startForeground() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "屏幕翻译悬浮窗",
                NotificationManager.IMPORTANCE_LOW
            )
            channel.lightColor = Color.BLUE
            channel.lockscreenVisibility = Notification.VISIBILITY_PRIVATE

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)

            val notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
                .setOngoing(true)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle("屏幕翻译悬浮窗")
                .setContentText("点击悬浮窗捕获屏幕内容进行翻译")
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setCategory(Notification.CATEGORY_SERVICE)

            startForeground(NOTIFICATION_ID, notificationBuilder.build())
        } else {
            startForeground(NOTIFICATION_ID, Notification())
        }
        Log.d(TAG, "前台服务已启动")
    }

    private fun createFloatingWindow() {
        try {
            // 初始化WindowManager
            windowManager = getSystemService(WINDOW_SERVICE) as WindowManager

            // 创建悬浮窗视图
            floatingView = LayoutInflater.from(this).inflate(R.layout.floating_window_layout, null)

            // 设置窗口参数
            val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_PHONE
            }

            params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                layoutFlag,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            )

            // 初始位置
            params?.gravity = Gravity.TOP or Gravity.START
            params?.x = 0 // 初始位置在左上角
            params?.y = 100

            // 设置拖动处理
            floatingView?.setOnTouchListener(object : View.OnTouchListener {
                private var startX = 0f
                private var startY = 0f
                private var moving = false

                override fun onTouch(view: View, event: MotionEvent): Boolean {
                    val screenWidth = windowManager?.defaultDisplay?.width ?: 0

                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            startX = event.rawX
                            startY = event.rawY
                            initialX = params?.x ?: 0
                            initialY = params?.y ?: 0
                            moving = false
                            return true
                        }
                        MotionEvent.ACTION_MOVE -> {
                            val dx = event.rawX - startX
                            val dy = event.rawY - startY

                            if (!moving && (Math.abs(dx) > 10 || Math.abs(dy) > 10)) {
                                moving = true
                            }

                            if (moving) {
                                // 更新悬浮窗位置
                                params?.x = initialX + dx.toInt()
                                params?.y = initialY + dy.toInt()

                                // 确保不会超出屏幕
                                params?.x = Math.max(0, Math.min(params?.x ?: 0, screenWidth - (view.width)))

                                windowManager?.updateViewLayout(floatingView, params)
                            }
                            return true
                        }
                        MotionEvent.ACTION_UP -> {
                            if (!moving) {
                                // 如果没有移动，视为点击
                                Log.d(TAG, "Floating window clicked")

                                // 发送广播通知Flutter端
                                val intent = Intent("com.example.video_sub_trans.FLOATING_WINDOW_CLICKED")
                                sendBroadcast(intent)

                                // 通知MainActivity进行屏幕捕获
                                val captureIntent = Intent("com.example.video_sub_trans.CAPTURE_SCREEN")
                                sendBroadcast(captureIntent)

                                // 延迟一段时间后，再次发送一个广播，确保屏幕捕获被处理
                                Handler(Looper.getMainLooper()).postDelayed({
                                    Log.d(TAG, "Sending delayed capture screen broadcast")
                                    val delayedCaptureIntent = Intent("com.example.video_sub_trans.CAPTURE_SCREEN")
                                    sendBroadcast(delayedCaptureIntent)
                                }, 500)
                            }
                            moving = false
                            return true
                        }
                    }
                    return false
                }
            })

            // 添加到窗口
            try {
                windowManager?.addView(floatingView, params)
                Log.d(TAG, "悬浮窗已创建并显示")
            } catch (e: Exception) {
                Log.e(TAG, "添加窗口视图失败", e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "创建悬浮窗失败", e)
        }
    }

    // 创建并显示翻译结果悬浮窗（透明悬浮窗）- 支持多个悬浮窗
    fun showTranslationOverlay(originalText: String, translatedText: String, x: Int, y: Int, width: Int, height: Int, index: Int = 0) {
        try {
            Log.d(TAG, "开始创建并显示翻译结果悬浮窗 #$index")

            // 如果已经存在该索引的翻译悬浮窗，先移除
            translationOverlayViews[index]?.let { existingView ->
                try {
                    windowManager?.removeView(existingView)
                    Log.d(TAG, "成功移除旧的翻译结果悬浮窗 #$index")
                } catch (e: Exception) {
                    Log.e(TAG, "移除旧的翻译结果悬浮窗 #$index 失败", e)
                }
                translationOverlayViews.remove(index)
                translationParams.remove(index)
            }

            // 初始化WindowManager（如果尚未初始化）
            if (windowManager == null) {
                windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
                Log.d(TAG, "初始化窗口管理器")
            }

            // 创建新的透明悬浮窗视图
            val overlayView: View
            try {
                Log.d(TAG, "开始创建透明悬浮窗视图 #$index")
                overlayView = LayoutInflater.from(this).inflate(R.layout.transparent_overlay_layout, null)
                Log.d(TAG, "成功创建透明悬浮窗视图 #$index")
            } catch (e: Exception) {
                Log.e(TAG, "创建透明悬浮窗视图 #$index 时出错", e)
                return
            }

            // 设置文本内容
            val textView = overlayView as TextView
            try {
                // 提取纯文本内容，去掉前缀
                val displayText = translatedText.replace("识别结果: ", "")
                Log.d(TAG, "设置文本内容 #$index: $displayText")

                // 设置文本内容
                textView.text = displayText
                textView.visibility = View.VISIBLE

                Log.d(TAG, "成功设置文本内容 #$index: $displayText")
            } catch (e: Exception) {
                Log.e(TAG, "设置文本内容 #$index 时出错", e)
                return
            }

            // 设置文本视图点击事件，点击文本复制到剪贴板
            textView.setOnClickListener {
                // 复制翻译结果到剪贴板
                val clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val clipData = ClipData.newPlainText("翻译结果", translatedText)
                clipboardManager.setPrimaryClip(clipData)

                // 显示提示
                Toast.makeText(this@FloatingWindowService, "已复制到剪贴板", Toast.LENGTH_SHORT).show()

                // 复制后自动关闭该悬浮窗
                Handler(Looper.getMainLooper()).postDelayed({
                    removeTranslationOverlay(index)
                }, 1000)
            }

            // 设置窗口参数
            val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_PHONE
            }

            // 创建窗口参数
            val windowParams = WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,  // 宽度自适应
                WindowManager.LayoutParams.WRAP_CONTENT,  // 高度自适应
                layoutFlag,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,  // 确保在屏幕内显示
                PixelFormat.TRANSLUCENT
            )

            // 设置位置为文本区域
            windowParams.gravity = Gravity.TOP or Gravity.START

            // 确保坐标在屏幕范围内
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val screenHeight = displayMetrics.heightPixels

            // 调整x坐标，确保在屏幕内
            windowParams.x = if (x < 0) 0 else if (x > screenWidth) screenWidth / 2 else x

            // 调整y坐标，确保在屏幕内
            windowParams.y = if (y < 0) 0 else if (y > screenHeight) screenHeight / 2 else y

            Log.d(TAG, "设置悬浮窗 #$index 位置和大小: x=${windowParams.x}, y=${windowParams.y}, 屏幕大小: ${screenWidth}x${screenHeight}")

            // 添加到窗口
            try {
                // 添加新的悬浮窗
                Log.d(TAG, "准备添加悬浮窗 #$index 到窗口管理器")
                windowManager?.addView(overlayView, windowParams)

                // 保存到集合中
                translationOverlayViews[index] = overlayView
                translationParams[index] = windowParams

                Log.d(TAG, "透明翻译结果悬浮窗 #$index 已创建并显示在位置 x=${windowParams.x}, y=${windowParams.y}")

                // 确保文本视图可见
                overlayView.visibility = View.VISIBLE

                // 强制重绘视图
                overlayView.invalidate()
            } catch (e: Exception) {
                Log.e(TAG, "添加悬浮窗 #$index 到窗口管理器失败", e)
                return
            }

        } catch (e: Exception) {
            Log.e(TAG, "创建透明翻译结果悬浮窗 #$index 失败", e)
        }
    }

    // 移除指定索引的翻译结果悬浮窗
    private fun removeTranslationOverlay(index: Int) {
        translationOverlayViews[index]?.let { view ->
            try {
                Log.d(TAG, "开始移除翻译结果悬浮窗 #$index")

                // 确保窗口管理器已初始化
                if (windowManager == null) {
                    windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
                    Log.d(TAG, "初始化窗口管理器")
                }

                windowManager?.removeView(view)
                translationOverlayViews.remove(index)
                translationParams.remove(index)
                Log.d(TAG, "成功移除翻译结果悬浮窗 #$index")
            } catch (e: IllegalArgumentException) {
                // 视图可能已经被移除，或者从未被添加
                Log.w(TAG, "视图 #$index 可能已经被移除，或者从未被添加: ${e.message}")
                translationOverlayViews.remove(index)
                translationParams.remove(index)
            } catch (e: Exception) {
                Log.e(TAG, "移除翻译结果悬浮窗 #$index 时发生其他错误", e)
            }
        } ?: run {
            Log.d(TAG, "无需移除翻译结果悬浮窗 #$index，因为它不存在")
        }
    }

    // 移除所有翻译结果悬浮窗
    private fun removeAllTranslationOverlays() {
        Log.d(TAG, "开始移除所有翻译结果悬浮窗，当前数量: ${translationOverlayViews.size}")

        // 确保窗口管理器已初始化
        if (windowManager == null) {
            windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
            Log.d(TAG, "初始化窗口管理器")
        }

        val indicesToRemove = translationOverlayViews.keys.toList()
        for (index in indicesToRemove) {
            removeTranslationOverlay(index)
        }

        Log.d(TAG, "已移除所有翻译结果悬浮窗")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "onStartCommand: ${intent?.action}")

        when (intent?.action) {
            ACTION_SHOW -> {
                Log.d(TAG, "显示悬浮窗命令: ${if (floatingView == null) "创建新窗口" else "显示现有窗口"}")
                if (floatingView == null) {
                    createFloatingWindow()
                } else {
                    floatingView?.visibility = View.VISIBLE
                }
                Log.d(TAG, "显示悬浮窗")
            }
            ACTION_HIDE -> {
                Log.d(TAG, "隐藏悬浮窗命令")
                floatingView?.visibility = View.GONE
                // 同时移除所有翻译结果悬浮窗
                removeAllTranslationOverlays()
                Log.d(TAG, "隐藏悬浮窗")
            }
            ACTION_CLEAR_ALL_TRANSLATIONS -> {
                Log.d(TAG, "清除所有翻译悬浮窗命令")
                removeAllTranslationOverlays()
            }
            ACTION_SHOW_TRANSLATION -> {
                Log.d(TAG, "SHOW_TRANSLATION_OVERLAY 命令接收")
                // 显示翻译结果悬浮窗
                val originalText = intent.getStringExtra("originalText") ?: "无原文"
                val translatedText = intent.getStringExtra("translatedText") ?: "无翻译结果"
                val x = intent.getIntExtra("x", 0)
                val y = intent.getIntExtra("y", 0)
                val width = intent.getIntExtra("width", 300)
                val height = intent.getIntExtra("height", 100)
                val index = intent.getIntExtra("index", 0) // 获取索引

                Log.d(TAG, "显示翻译结果悬浮窗 #$index: originalText=${if (originalText.length > 50) originalText.substring(0, 50) + "..." else originalText}, translatedText=${if (translatedText.length > 50) translatedText.substring(0, 50) + "..." else translatedText}")
                Log.d(TAG, "位置信息: x=$x, y=$y, width=$width, height=$height")

                try {
                    // 确保文本不为空
                    val textToShow = if (translatedText.isBlank() || translatedText == "无翻译结果") {
                        "无法识别文本"
                    } else {
                        translatedText
                    }

                    Log.d(TAG, "准备显示文本 #$index: $textToShow")

                    // 确保位置参数合理
                    val adjustedX = if (x < 0) 0 else x
                    val adjustedY = if (y < 0) 0 else y
                    val adjustedWidth = if (width <= 0) 300 else width
                    val adjustedHeight = if (height <= 0) 100 else height

                    Log.d(TAG, "调整后的位置 #$index: x=$adjustedX, y=$adjustedY, width=$adjustedWidth, height=$adjustedHeight")

                    // 确保主悬浮窗已显示
                    if (floatingView == null) {
                        Log.d(TAG, "主悬浮窗不存在，先创建主悬浮窗")
                        createFloatingWindow()
                    }

                    // 显示翻译结果悬浮窗
                    Log.d(TAG, "开始显示翻译结果悬浮窗 #$index")
                    showTranslationOverlay(originalText, textToShow, adjustedX, adjustedY, adjustedWidth, adjustedHeight, index)
                } catch (e: Exception) {
                    Log.e(TAG, "显示翻译结果悬浮窗 #$index 失败", e)
                }
            }
            null -> {
                Log.d(TAG, "服务启动，但无特定命令，默认创建悬浮窗")
                if (floatingView == null) {
                    createFloatingWindow()
                }
            }
            else -> {
                Log.d(TAG, "未知命令: ${intent.action}")
            }
        }

        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        // 移除主悬浮窗
        if (floatingView != null) {
            try {
                windowManager?.removeView(floatingView)
            } catch (e: Exception) {
                Log.e(TAG, "移除悬浮窗视图失败", e)
            }
            floatingView = null
        }

        // 移除翻译结果悬浮窗
        removeTranslationOverlay()

        Log.d(TAG, "销毁悬浮窗服务")
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
}