package com.example.video_sub_trans.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import android.widget.LinearLayout
import android.widget.FrameLayout
import android.widget.Toast
import androidx.core.app.NotificationCompat
import com.example.video_sub_trans.R

class FloatingWindowService : Service() {
    private val TAG = "FloatingWindowService"

    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var params: WindowManager.LayoutParams? = null

    // 翻译结果悬浮窗
    private var translationOverlayView: View? = null
    private var translationParams: WindowManager.LayoutParams? = null

    private var initialTouchX: Float = 0f
    private var initialTouchY: Float = 0f
    private var initialX: Int = 0
    private var initialY: Int = 0

    private val NOTIFICATION_ID = 2
    private val CHANNEL_ID = "FloatingWindowChannel"

    companion object {
        const val ACTION_SHOW = "SHOW_FLOATING_WINDOW"
        const val ACTION_HIDE = "HIDE_FLOATING_WINDOW"
        const val ACTION_SHOW_TRANSLATION = "SHOW_TRANSLATION_OVERLAY"
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate 创建悬浮窗服务")
        startForeground()
    }

    private fun startForeground() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "屏幕翻译悬浮窗",
                NotificationManager.IMPORTANCE_LOW
            )
            channel.lightColor = Color.BLUE
            channel.lockscreenVisibility = Notification.VISIBILITY_PRIVATE

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)

            val notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
                .setOngoing(true)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle("屏幕翻译悬浮窗")
                .setContentText("点击悬浮窗捕获屏幕内容进行翻译")
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setCategory(Notification.CATEGORY_SERVICE)

            startForeground(NOTIFICATION_ID, notificationBuilder.build())
        } else {
            startForeground(NOTIFICATION_ID, Notification())
        }
        Log.d(TAG, "前台服务已启动")
    }

    private fun createFloatingWindow() {
        try {
            // 初始化WindowManager
            windowManager = getSystemService(WINDOW_SERVICE) as WindowManager

            // 创建悬浮窗视图
            floatingView = LayoutInflater.from(this).inflate(R.layout.floating_window_layout, null)

            // 设置窗口参数
            val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_PHONE
            }

            params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                layoutFlag,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            )

            // 初始位置
            params?.gravity = Gravity.TOP or Gravity.START
            params?.x = 0 // 初始位置在左上角
            params?.y = 100

            // 设置拖动处理
            floatingView?.setOnTouchListener(object : View.OnTouchListener {
                private var startX = 0f
                private var startY = 0f
                private var moving = false

                override fun onTouch(view: View, event: MotionEvent): Boolean {
                    val screenWidth = windowManager?.defaultDisplay?.width ?: 0

                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            startX = event.rawX
                            startY = event.rawY
                            initialX = params?.x ?: 0
                            initialY = params?.y ?: 0
                            moving = false
                            return true
                        }
                        MotionEvent.ACTION_MOVE -> {
                            val dx = event.rawX - startX
                            val dy = event.rawY - startY

                            if (!moving && (Math.abs(dx) > 10 || Math.abs(dy) > 10)) {
                                moving = true
                            }

                            if (moving) {
                                // 更新悬浮窗位置
                                params?.x = initialX + dx.toInt()
                                params?.y = initialY + dy.toInt()

                                // 确保不会超出屏幕
                                params?.x = Math.max(0, Math.min(params?.x ?: 0, screenWidth - (view.width)))

                                windowManager?.updateViewLayout(floatingView, params)
                            }
                            return true
                        }
                        MotionEvent.ACTION_UP -> {
                            if (!moving) {
                                // 如果没有移动，视为点击
                                Log.d(TAG, "Floating window clicked")

                                // 发送广播通知Flutter端
                                val intent = Intent("com.example.video_sub_trans.FLOATING_WINDOW_CLICKED")
                                sendBroadcast(intent)

                                // 通知MainActivity进行屏幕捕获
                                val captureIntent = Intent("com.example.video_sub_trans.CAPTURE_SCREEN")
                                sendBroadcast(captureIntent)

                                // 延迟一段时间后，再次发送一个广播，确保屏幕捕获被处理
                                Handler(Looper.getMainLooper()).postDelayed({
                                    Log.d(TAG, "Sending delayed capture screen broadcast")
                                    val delayedCaptureIntent = Intent("com.example.video_sub_trans.CAPTURE_SCREEN")
                                    sendBroadcast(delayedCaptureIntent)
                                }, 500)
                            }
                            moving = false
                            return true
                        }
                    }
                    return false
                }
            })

            // 添加到窗口
            try {
                windowManager?.addView(floatingView, params)
                Log.d(TAG, "悬浮窗已创建并显示")
            } catch (e: Exception) {
                Log.e(TAG, "添加窗口视图失败", e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "创建悬浮窗失败", e)
        }
    }

    // 创建并显示翻译结果悬浮窗（透明悬浮窗）
    fun showTranslationOverlay(originalText: String, translatedText: String, x: Int, y: Int, width: Int, height: Int) {
        try {
            Log.d(TAG, "开始创建并显示翻译结果悬浮窗")

            // 如果已经存在翻译悬浮窗，先移除视图，但不设置为null
            if (isTranslationOverlayAdded && translationOverlayView != null) {
                try {
                    windowManager?.removeView(translationOverlayView)
                    Log.d(TAG, "成功移除旧的翻译结果悬浮窗")
                    isTranslationOverlayAdded = false
                } catch (e: Exception) {
                    Log.e(TAG, "移除旧的翻译结果悬浮窗失败", e)
                }
            }

            // 初始化WindowManager（如果尚未初始化）
            if (windowManager == null) {
                windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
                Log.d(TAG, "初始化窗口管理器")
            }

            // 创建新的透明悬浮窗视图
            try {
                Log.d(TAG, "开始创建透明悬浮窗视图")
                translationOverlayView = LayoutInflater.from(this).inflate(R.layout.transparent_overlay_layout, null)

                if (translationOverlayView == null) {
                    Log.e(TAG, "无法创建透明悬浮窗视图，inflate 返回 null")
                    return
                }

                Log.d(TAG, "成功创建透明悬浮窗视图")
            } catch (e: Exception) {
                Log.e(TAG, "创建透明悬浮窗视图时出错", e)
                return
            }

            // 确保视图是TextView类型
            Log.d(TAG, "检查视图类型")

            // 获取TextView引用
            val textView = translationOverlayView as? TextView
            if (textView == null) {
                Log.e(TAG, "translationOverlayView 不是 TextView 类型: ${translationOverlayView?.javaClass?.name}")

                // 创建一个新的TextView作为备用方案
                Log.d(TAG, "创建一个新的TextView作为备用方案")
                val newTextView = TextView(this)
                newTextView.layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
                newTextView.setBackgroundColor(Color.parseColor("#E0FF0000"))
                newTextView.setPadding(16, 8, 16, 8)
                newTextView.setTextColor(Color.WHITE)
                newTextView.textSize = 24f
                newTextView.setShadowLayer(3f, 2f, 2f, Color.BLACK)
                newTextView.typeface = Typeface.DEFAULT_BOLD
                newTextView.visibility = View.VISIBLE

                // 替换原来的视图
                translationOverlayView = newTextView
                Log.d(TAG, "已创建新的TextView")
            }

            // 设置文本内容
            try {
                // 提取纯文本内容，去掉前缀
                val displayText = translatedText.replace("识别结果: ", "")
                Log.d(TAG, "设置文本内容: $displayText")

                // 设置文本内容
                (translationOverlayView as TextView).text = displayText

                // 确保文本视图可见
                translationOverlayView?.visibility = View.VISIBLE

                Log.d(TAG, "成功设置文本内容: $displayText")
            } catch (e: Exception) {
                Log.e(TAG, "设置文本内容时出错", e)
                return
            }

            // 设置文本视图点击事件，点击文本复制到剪贴板
            translationOverlayView?.setOnClickListener {
                // 复制翻译结果到剪贴板
                val clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                val clipData = ClipData.newPlainText("翻译结果", translatedText)
                clipboardManager.setPrimaryClip(clipData)

                // 显示提示
                Toast.makeText(this, "已复制到剪贴板", Toast.LENGTH_SHORT).show()

                // 复制后自动关闭悬浮窗
                Handler(Looper.getMainLooper()).postDelayed({
                    removeTranslationOverlay()
                }, 1000)
            }

            // 设置窗口参数
            val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_PHONE
            }

            // 使用更可靠的窗口参数
            translationParams = WindowManager.LayoutParams(
                if (width > 0) width else WindowManager.LayoutParams.WRAP_CONTENT,  // 使用传入的宽度，如果为0则自适应
                WindowManager.LayoutParams.WRAP_CONTENT,  // 高度自适应
                layoutFlag,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,  // 确保在屏幕内显示
                PixelFormat.TRANSLUCENT
            )

            // 设置位置为文本区域
            translationParams?.gravity = Gravity.TOP or Gravity.START

            // 确保坐标在屏幕范围内
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val screenHeight = displayMetrics.heightPixels

            // 调整x坐标，确保在屏幕内
            translationParams?.x = if (x < 0) 0 else if (x > screenWidth) screenWidth / 2 else x

            // 调整y坐标，确保在屏幕内
            translationParams?.y = if (y < 0) 0 else if (y > screenHeight) screenHeight / 2 else y

            Log.d(TAG, "设置悬浮窗位置和大小: x=${translationParams?.x}, y=${translationParams?.y}, width=${translationParams?.width}, 屏幕大小: ${screenWidth}x${screenHeight}")

            // 添加到窗口
            try {
                // 确保窗口管理器已初始化
                if (windowManager == null) {
                    windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
                    Log.d(TAG, "初始化窗口管理器")
                }

                // 再次检查视图是否存在
                if (translationOverlayView == null) {
                    Log.e(TAG, "无法添加悬浮窗，因为视图为 null")
                    return
                }

                // 添加新的悬浮窗
                Log.d(TAG, "准备添加悬浮窗到窗口管理器")
                try {
                    windowManager?.addView(translationOverlayView, translationParams)
                    isTranslationOverlayAdded = true
                    Log.d(TAG, "透明翻译结果悬浮窗已创建并显示在位置 x=${translationParams?.x}, y=${translationParams?.y}, width=${translationParams?.width}, height=${translationParams?.height}")

                    // 确保文本视图可见
                    translationOverlayView?.visibility = View.VISIBLE

                    // 强制重绘视图
                    translationOverlayView?.invalidate()
                } catch (e: Exception) {
                    Log.e(TAG, "添加悬浮窗到窗口管理器失败", e)

                    // 尝试使用更简单的参数重试一次
                    try {
                        Log.d(TAG, "尝试使用更简单的参数重试添加悬浮窗")
                        val simpleParams = WindowManager.LayoutParams(
                            WindowManager.LayoutParams.WRAP_CONTENT,
                            WindowManager.LayoutParams.WRAP_CONTENT,
                            layoutFlag,
                            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                            PixelFormat.TRANSLUCENT
                        )
                        simpleParams.gravity = Gravity.CENTER
                        windowManager?.addView(translationOverlayView, simpleParams)
                        isTranslationOverlayAdded = true
                        Log.d(TAG, "使用简单参数添加悬浮窗成功")
                    } catch (e2: Exception) {
                        Log.e(TAG, "使用简单参数添加悬浮窗仍然失败", e2)
                        isTranslationOverlayAdded = false
                        return
                    }
                }

                // 延迟一段时间后，检查悬浮窗是否正常显示并尝试修复
                Handler(Looper.getMainLooper()).postDelayed({
                    if (translationOverlayView != null && isTranslationOverlayAdded) {
                        Log.d(TAG, "悬浮窗检查: 悬浮窗仍然存在")

                        try {
                            // 直接检查文本视图（整个视图就是文本视图）
                            (translationOverlayView as? TextView)?.let { textView ->
                                Log.d(TAG, "悬浮窗文本内容: ${textView.text}")

                                // 确保文本视图可见
                                textView.visibility = View.VISIBLE

                                // 强制重绘
                                textView.invalidate()

                                Log.d(TAG, "再次确认文本视图可见性")
                            } ?: run {
                                Log.e(TAG, "悬浮窗检查: translationOverlayView 不是 TextView 类型，尝试修复")

                                // 尝试修复：创建新的TextView
                                val newTextView = TextView(this@FloatingWindowService)
                                newTextView.layoutParams = ViewGroup.LayoutParams(
                                    ViewGroup.LayoutParams.WRAP_CONTENT,
                                    ViewGroup.LayoutParams.WRAP_CONTENT
                                )
                                newTextView.setBackgroundColor(Color.parseColor("#E0FF0000"))
                                newTextView.setPadding(16, 8, 16, 8)
                                newTextView.setTextColor(Color.WHITE)
                                newTextView.textSize = 24f
                                newTextView.text = translatedText
                                newTextView.visibility = View.VISIBLE

                                // 移除旧视图，添加新视图
                                try {
                                    windowManager?.removeView(translationOverlayView)
                                    windowManager?.addView(newTextView, WindowManager.LayoutParams(
                                        WindowManager.LayoutParams.WRAP_CONTENT,
                                        WindowManager.LayoutParams.WRAP_CONTENT,
                                        layoutFlag,
                                        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                                        PixelFormat.TRANSLUCENT
                                    ).apply { gravity = Gravity.CENTER })

                                    translationOverlayView = newTextView
                                    Log.d(TAG, "已替换为新的TextView")
                                } catch (e: Exception) {
                                    Log.e(TAG, "替换视图失败", e)
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "检查悬浮窗时出错", e)
                        }
                    } else {
                        Log.d(TAG, "悬浮窗检查: 悬浮窗已被移除或不存在，尝试重新创建")

                        // 尝试重新创建悬浮窗
                        try {
                            val newTextView = TextView(this@FloatingWindowService)
                            newTextView.layoutParams = ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.WRAP_CONTENT,
                                ViewGroup.LayoutParams.WRAP_CONTENT
                            )
                            newTextView.setBackgroundColor(Color.parseColor("#E0FF0000"))
                            newTextView.setPadding(16, 8, 16, 8)
                            newTextView.setTextColor(Color.WHITE)
                            newTextView.textSize = 24f
                            newTextView.text = translatedText
                            newTextView.visibility = View.VISIBLE

                            val simpleParams = WindowManager.LayoutParams(
                                WindowManager.LayoutParams.WRAP_CONTENT,
                                WindowManager.LayoutParams.WRAP_CONTENT,
                                layoutFlag,
                                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                                PixelFormat.TRANSLUCENT
                            )
                            simpleParams.gravity = Gravity.CENTER

                            windowManager?.addView(newTextView, simpleParams)
                            translationOverlayView = newTextView
                            isTranslationOverlayAdded = true
                            Log.d(TAG, "已重新创建悬浮窗")
                        } catch (e: Exception) {
                            Log.e(TAG, "重新创建悬浮窗失败", e)
                        }
                    }
                }, 500)
            } catch (e: Exception) {
                Log.e(TAG, "添加透明翻译结果悬浮窗失败", e)
                isTranslationOverlayAdded = false
            }

        } catch (e: Exception) {
            Log.e(TAG, "创建透明翻译结果悬浮窗失败", e)
        }
    }

    // 标记悬浮窗是否已添加到窗口管理器
    private var isTranslationOverlayAdded = false

    // 移除翻译结果悬浮窗
    private fun removeTranslationOverlay() {
        if (translationOverlayView != null) {
            try {
                Log.d(TAG, "开始移除翻译结果悬浮窗")

                // 确保窗口管理器已初始化
                if (windowManager == null) {
                    windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
                    Log.d(TAG, "初始化窗口管理器")
                }

                // 只有在悬浮窗已添加到窗口管理器时才尝试移除它
                if (isTranslationOverlayAdded) {
                    try {
                        windowManager?.removeView(translationOverlayView)
                        Log.d(TAG, "成功移除翻译结果悬浮窗")
                        isTranslationOverlayAdded = false
                    } catch (e: IllegalArgumentException) {
                        // 视图可能已经被移除，或者从未被添加
                        Log.w(TAG, "视图可能已经被移除，或者从未被添加: ${e.message}")
                        isTranslationOverlayAdded = false
                    } catch (e: Exception) {
                        Log.e(TAG, "移除翻译结果悬浮窗时发生其他错误", e)
                    }
                } else {
                    Log.d(TAG, "悬浮窗未添加到窗口管理器，无需移除")
                }
            } catch (e: Exception) {
                Log.e(TAG, "移除翻译结果悬浮窗失败", e)
            }
        } else {
            Log.d(TAG, "无需移除翻译结果悬浮窗，因为它不存在")
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "onStartCommand: ${intent?.action}")

        when (intent?.action) {
            ACTION_SHOW -> {
                Log.d(TAG, "显示悬浮窗命令: ${if (floatingView == null) "创建新窗口" else "显示现有窗口"}")
                if (floatingView == null) {
                    createFloatingWindow()
                } else {
                    floatingView?.visibility = View.VISIBLE
                }
                Log.d(TAG, "显示悬浮窗")
            }
            ACTION_HIDE -> {
                Log.d(TAG, "隐藏悬浮窗命令")
                floatingView?.visibility = View.GONE
                // 同时移除翻译结果悬浮窗
                removeTranslationOverlay()
                Log.d(TAG, "隐藏悬浮窗")
            }
            ACTION_SHOW_TRANSLATION -> {
                Log.d(TAG, "SHOW_TRANSLATION_OVERLAY 命令接收")
                // 显示翻译结果悬浮窗
                val originalText = intent.getStringExtra("originalText") ?: "无原文"
                val translatedText = intent.getStringExtra("translatedText") ?: "无翻译结果"
                val x = intent.getIntExtra("x", 0)
                val y = intent.getIntExtra("y", 0)
                val width = intent.getIntExtra("width", 300)
                val height = intent.getIntExtra("height", 100)

                Log.d(TAG, "显示翻译结果悬浮窗: originalText=${if (originalText.length > 50) originalText.substring(0, 50) + "..." else originalText}, translatedText=${if (translatedText.length > 50) translatedText.substring(0, 50) + "..." else translatedText}")
                Log.d(TAG, "位置信息: x=$x, y=$y, width=$width, height=$height")

                try {
                    // 检查布局文件是否存在
                    val layoutId = resources.getIdentifier("transparent_overlay_layout", "layout", packageName)
                    if (layoutId == 0) {
                        Log.e(TAG, "找不到布局文件 transparent_overlay_layout")
                    } else {
                        Log.d(TAG, "找到布局文件 transparent_overlay_layout, id=$layoutId")
                    }

                    // 确保文本不为空
                    val textToShow = if (translatedText.isBlank() || translatedText == "无翻译结果") {
                        "无法识别文本"
                    } else {
                        translatedText
                    }

                    Log.d(TAG, "准备显示文本: $textToShow")

                    // 确保位置参数合理
                    val adjustedX = if (x < 0) 0 else x
                    val adjustedY = if (y < 0) 0 else y
                    val adjustedWidth = if (width <= 0) 300 else width
                    val adjustedHeight = if (height <= 0) 100 else height

                    Log.d(TAG, "调整后的位置: x=$adjustedX, y=$adjustedY, width=$adjustedWidth, height=$adjustedHeight")

                    // 确保主悬浮窗已显示
                    if (floatingView == null) {
                        Log.d(TAG, "主悬浮窗不存在，先创建主悬浮窗")
                        createFloatingWindow()
                    }

                    // 显示翻译结果悬浮窗
                    Log.d(TAG, "开始显示翻译结果悬浮窗")

                    // 确保在显示翻译结果前不会移除悬浮窗
                    if (translationOverlayView == null) {
                        Log.d(TAG, "translationOverlayView 为 null，将创建新的悬浮窗")
                    } else {
                        Log.d(TAG, "translationOverlayView 不为 null，将更新现有悬浮窗")
                    }

                    showTranslationOverlay(originalText, textToShow, adjustedX, adjustedY, adjustedWidth, adjustedHeight)
                } catch (e: Exception) {
                    Log.e(TAG, "显示翻译结果悬浮窗失败", e)
                }
            }
            null -> {
                Log.d(TAG, "服务启动，但无特定命令，默认创建悬浮窗")
                if (floatingView == null) {
                    createFloatingWindow()
                }
            }
            else -> {
                Log.d(TAG, "未知命令: ${intent.action}")
            }
        }

        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        // 移除主悬浮窗
        if (floatingView != null) {
            try {
                windowManager?.removeView(floatingView)
            } catch (e: Exception) {
                Log.e(TAG, "移除悬浮窗视图失败", e)
            }
            floatingView = null
        }

        // 移除翻译结果悬浮窗
        removeTranslationOverlay()

        Log.d(TAG, "销毁悬浮窗服务")
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
}