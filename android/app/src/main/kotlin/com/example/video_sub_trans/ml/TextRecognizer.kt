package com.example.video_sub_trans.ml

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import android.util.Log
import com.google.android.gms.tasks.Task
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.Text
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.chinese.ChineseTextRecognizerOptions
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import java.io.IOException

/**
 * ML Kit OCR文本识别器封装类
 * 支持中文和拉丁语系离线文本识别
 */
class TextRecognizer(private val context: Context) {
    private val TAG = "TextRecognizer"

    // 拉丁文字识别器（英文等）
    private val latinRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)

    // 中文识别器
    private val chineseRecognizer = TextRecognition.getClient(ChineseTextRecognizerOptions.Builder().build())

    /**
     * 根据图片URI进行文本识别
     * @param imageUri 图片URI
     * @param preferChinese 优先使用中文识别器
     * @param callback 识别结果回调
     */
    fun recognizeText(imageUri: Uri, preferChinese: Boolean = true, callback: (String?, Exception?) -> Unit) {
        try {
            val inputImage = InputImage.fromFilePath(context, imageUri)
            processImage(inputImage, preferChinese, callback)
        } catch (e: IOException) {
            Log.e(TAG, "Error creating input image from URI", e)
            callback(null, e)
        }
    }

    /**
     * 根据Bitmap进行文本识别
     * @param bitmap 图片Bitmap
     * @param preferChinese 优先使用中文识别器
     * @param callback 识别结果回调
     */
    fun recognizeText(bitmap: Bitmap, preferChinese: Boolean = true, callback: (String?, Exception?) -> Unit) {
        val inputImage = InputImage.fromBitmap(bitmap, 0)
        processImage(inputImage, preferChinese, callback)
    }

    /**
     * 根据Bitmap进行文本识别，并返回文本块位置信息
     * @param bitmap 图片Bitmap
     * @param preferChinese 优先使用中文识别器
     * @param callback 识别结果回调，返回文本和位置信息的Map
     */
    fun recognizeTextWithBounds(bitmap: Bitmap, preferChinese: Boolean = true, callback: (Map<String, List<Float>>?, Exception?) -> Unit) {
        val inputImage = InputImage.fromBitmap(bitmap, 0)
        processImageWithBounds(inputImage, preferChinese, callback)
    }

    /**
     * 处理InputImage图像进行文本识别
     */
    private fun processImage(inputImage: InputImage, preferChinese: Boolean, callback: (String?, Exception?) -> Unit) {
        // 根据优先级选择识别器
        val recognizer = if (preferChinese) chineseRecognizer else latinRecognizer

        recognizer.process(inputImage)
            .addOnSuccessListener { text ->
                // 如果首选识别器未能识别出足够的文本，尝试使用另一个识别器
                if (text.text.trim().length < 10 && preferChinese) {
                    // 中文识别器识别结果太少，尝试拉丁文识别器
                    Log.d(TAG, "Chinese recognizer result too short, trying Latin recognizer")
                    latinRecognizer.process(inputImage)
                        .addOnSuccessListener { latinText ->
                            callback(processRecognizedText(latinText), null)
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Latin text recognition failed", e)
                            // 返回中文识别器的结果
                            callback(processRecognizedText(text), null)
                        }
                } else if (text.text.trim().length < 10 && !preferChinese) {
                    // 拉丁文识别器识别结果太少，尝试中文识别器
                    Log.d(TAG, "Latin recognizer result too short, trying Chinese recognizer")
                    chineseRecognizer.process(inputImage)
                        .addOnSuccessListener { chineseText ->
                            callback(processRecognizedText(chineseText), null)
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Chinese text recognition failed", e)
                            // 返回拉丁文识别器的结果
                            callback(processRecognizedText(text), null)
                        }
                } else {
                    // 直接返回首选识别器的结果
                    callback(processRecognizedText(text), null)
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Text recognition failed", e)
                callback(null, e)
            }
    }

    /**
     * 处理识别结果，提取有用的文本信息
     */
    private fun processRecognizedText(text: Text): String {
        val sb = StringBuilder()

        for (block in text.textBlocks) {
            for (line in block.lines) {
                sb.append(line.text).append("\n")
            }
            sb.append("\n")
        }

        val result = sb.toString().trim()
        Log.d(TAG, "Recognized text: ${if (result.length > 50) result.substring(0, 50) + "..." else result}")
        return result
    }

    /**
     * 处理InputImage图像进行文本识别，并返回文本块位置信息
     */
    private fun processImageWithBounds(inputImage: InputImage, preferChinese: Boolean, callback: (Map<String, List<Float>>?, Exception?) -> Unit) {
        // 根据优先级选择识别器
        val recognizer = if (preferChinese) chineseRecognizer else latinRecognizer

        recognizer.process(inputImage)
            .addOnSuccessListener { text ->
                // 如果首选识别器未能识别出足够的文本，尝试使用另一个识别器
                if (text.text.trim().length < 10 && preferChinese) {
                    // 中文识别器识别结果太少，尝试拉丁文识别器
                    Log.d(TAG, "Chinese recognizer result too short, trying Latin recognizer")
                    latinRecognizer.process(inputImage)
                        .addOnSuccessListener { latinText ->
                            callback(processRecognizedTextWithBounds(latinText), null)
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Latin text recognition failed", e)
                            // 返回中文识别器的结果
                            callback(processRecognizedTextWithBounds(text), null)
                        }
                } else if (text.text.trim().length < 10 && !preferChinese) {
                    // 拉丁文识别器识别结果太少，尝试中文识别器
                    Log.d(TAG, "Latin recognizer result too short, trying Chinese recognizer")
                    chineseRecognizer.process(inputImage)
                        .addOnSuccessListener { chineseText ->
                            callback(processRecognizedTextWithBounds(chineseText), null)
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Chinese text recognition failed", e)
                            // 返回拉丁文识别器的结果
                            callback(processRecognizedTextWithBounds(text), null)
                        }
                } else {
                    // 直接返回首选识别器的结果
                    callback(processRecognizedTextWithBounds(text), null)
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Text recognition with bounds failed", e)
                callback(null, e)
            }
    }

    /**
     * 处理识别结果，提取文本和位置信息
     */
    private fun processRecognizedTextWithBounds(text: Text): Map<String, List<Float>> {
        val result = mutableMapOf<String, List<Float>>()

        for (block in text.textBlocks) {
            for (line in block.lines) {
                val boundingBox = line.boundingBox
                if (boundingBox != null) {
                    // 保存文本和边界框信息 [left, top, right, bottom]
                    result[line.text] = listOf(
                        boundingBox.left.toFloat(),
                        boundingBox.top.toFloat(),
                        boundingBox.right.toFloat(),
                        boundingBox.bottom.toFloat()
                    )
                }
            }
        }

        Log.d(TAG, "Recognized ${result.size} text blocks with bounds")
        return result
    }

    /**
     * 关闭识别器，释放资源
     */
    fun close() {
        latinRecognizer.close()
        chineseRecognizer.close()
    }
}