import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'state/providers.dart';
import 'routes/app_router.dart';
import 'storage/kv.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set system UI style
  if (Platform.isAndroid) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        systemNavigationBarColor:
            Colors.white, // Set navigation bar background color
        systemNavigationBarIconBrightness:
            Brightness.dark, // Navigation bar buttons dark color
        statusBarColor: Colors.transparent, // Transparent status bar
        statusBarIconBrightness: Brightness.dark, // Status bar icons dark color
      ),
    );
  }

  // Initialize key-value storage
  await KV.init(
    mode: Platform.isAndroid ? KVMode.multi : KVMode.single,
    cryptKey: 'videosubtrans',
  );

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return AppStateProvider(
      child: MaterialApp.router(
        routerConfig: router,
        title: 'Video Sub Trans',
        builder: (context, child) {
          // Add custom handling to ensure system UI style
          if (Platform.isAndroid) {
            // Apply system UI style
            SystemChrome.setSystemUIOverlayStyle(
              const SystemUiOverlayStyle(
                systemNavigationBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
              ),
            );
          }

          return MediaQuery(
            // Prevent keyboard from resizing layout
            data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
            child: child!,
          );
        },
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
          appBarTheme: const AppBarTheme(
            systemOverlayStyle: SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.dark,
              systemNavigationBarColor: Colors.white,
              systemNavigationBarIconBrightness: Brightness.dark,
            ),
          ),
        ),
      ),
    );
  }
}
