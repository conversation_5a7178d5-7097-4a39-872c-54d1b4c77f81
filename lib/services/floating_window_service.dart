import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:desktop_floating_window/desktop_floating_window.dart';

class FloatingWindowService {
  static FloatingWindowService? _instance;
  static const MethodChannel _channel = MethodChannel('app/floating_window');
  static const String TAG = "FloatingWindowService";

  // Private constructor
  FloatingWindowService._();

  // Singleton instance
  static FloatingWindowService get instance {
    _instance ??= FloatingWindowService._();
    return _instance!;
  }

  // Stream controller for floating window events
  final StreamController<String> _eventController =
      StreamController<String>.broadcast();
  Stream<String> get onFloatingWindowEvent => _eventController.stream;

  // Subscription for window click events
  StreamSubscription? _clickSubscription;

  // Is floating window visible
  bool _isVisible = false;
  bool get isVisible => _isVisible;

  // Initialize the service
  Future<void> initialize() async {
    print(
      '$TAG: Initializing service on platform: ${Platform.operatingSystem}',
    );
    if (Platform.isAndroid) {
      // Set up method call handler for Android
      _channel.setMethodCallHandler(_handleMethodCall);
      print('$TAG: Method call handler set up for Android');
    } else if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
      // Subscribe to floating window click events for desktop
      _clickSubscription = DesktopFloatingWindow.onFloatingWindowClick.listen(
        _handleWindowClick,
      );
      print('$TAG: Click subscription set up for desktop');
    }
  }

  // Handle method calls from platform
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    print('$TAG: Received method call: ${call.method}');
    switch (call.method) {
      case 'onPermissionResult':
        final bool hasPermission = call.arguments as bool;
        print('$TAG: Permission result: $hasPermission');
        _eventController.add('permission:$hasPermission');
        return null;
      case 'onWindowClick':
        print('$TAG: Window clicked');
        _eventController.add('click');
        return null;
      default:
        print('$TAG: Unhandled method: ${call.method}');
        return null;
    }
  }

  // Dispose resources
  void dispose() {
    print('$TAG: Disposing resources');
    _clickSubscription?.cancel();
    _eventController.close();
  }

  // Handle window click events
  void _handleWindowClick(String event) {
    print('$TAG: Window click event: $event');
    _eventController.add(event);
  }

  // Check if floating window permission is granted
  Future<bool> checkPermission() async {
    print('$TAG: Checking permission');
    try {
      if (Platform.isAndroid) {
        final result =
            await _channel.invokeMethod<bool>('checkPermission') ?? false;
        print('$TAG: Android permission check result: $result');
        return result;
      } else if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        final result = await DesktopFloatingWindow.checkPermission();
        print('$TAG: Desktop permission check result: $result');
        return result;
      }
      print('$TAG: Unsupported platform for permission check');
      return false;
    } catch (e, stacktrace) {
      print('$TAG: Error checking permission: $e');
      print('$TAG: Stack trace: $stacktrace');
      return false;
    }
  }

  // Request floating window permission
  Future<bool> requestPermission() async {
    print('$TAG: Requesting permission');
    try {
      if (Platform.isAndroid) {
        final result =
            await _channel.invokeMethod<bool>('requestPermission') ?? false;
        print('$TAG: Android permission request result: $result');
        return result;
      } else if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        final result = await DesktopFloatingWindow.requestPermission();
        print('$TAG: Desktop permission request result: $result');
        return result;
      }
      print('$TAG: Unsupported platform for permission request');
      return false;
    } catch (e, stacktrace) {
      print('$TAG: Error requesting permission: $e');
      print('$TAG: Stack trace: $stacktrace');
      return false;
    }
  }

  // Show the floating window
  Future<bool> showFloatingWindow() async {
    print('$TAG: Showing floating window');
    try {
      if (Platform.isAndroid) {
        print('$TAG: Calling Android show method');
        try {
          final result = await _channel.invokeMethod<bool>('show') ?? false;
          print('$TAG: Android show result: $result');
          if (result) {
            _isVisible = true;
          }
          return result;
        } catch (e, stacktrace) {
          print('$TAG: Error in Android show method: $e');
          print('$TAG: Stack trace: $stacktrace');
          return false;
        }
      } else if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        print('$TAG: Calling desktop show method');
        final result = await DesktopFloatingWindow.showFloatingWindow();
        print('$TAG: Desktop show result: $result');
        if (result) {
          _isVisible = true;
        }
        return result;
      }
      print('$TAG: Unsupported platform for show');
      return false;
    } catch (e, stacktrace) {
      print('$TAG: Error showing floating window: $e');
      print('$TAG: Stack trace: $stacktrace');
      return false;
    }
  }

  // Hide the floating window
  Future<bool> hideFloatingWindow() async {
    print('$TAG: Hiding floating window');
    try {
      if (Platform.isAndroid) {
        print('$TAG: Calling Android hide method');
        try {
          final result = await _channel.invokeMethod<bool>('hide') ?? false;
          print('$TAG: Android hide result: $result');
          if (result) {
            _isVisible = false;
          }
          return result;
        } catch (e, stacktrace) {
          print('$TAG: Error in Android hide method: $e');
          print('$TAG: Stack trace: $stacktrace');
          return false;
        }
      } else if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        print('$TAG: Calling desktop hide method');
        final result = await DesktopFloatingWindow.hideFloatingWindow();
        print('$TAG: Desktop hide result: $result');
        if (result) {
          _isVisible = false;
        }
        return result;
      }
      print('$TAG: Unsupported platform for hide');
      return false;
    } catch (e, stacktrace) {
      print('$TAG: Error hiding floating window: $e');
      print('$TAG: Stack trace: $stacktrace');
      return false;
    }
  }

  // Check if floating window is visible
  Future<bool> isFloatingWindowVisible() async {
    print('$TAG: Checking if floating window is visible');
    try {
      if (Platform.isAndroid) {
        final result = await _channel.invokeMethod<bool>('isVisible') ?? false;
        print('$TAG: Android isVisible result: $result');
        return result;
      }
      print('$TAG: Using cached visibility: $_isVisible');
      return _isVisible;
    } catch (e, stacktrace) {
      print('$TAG: Error checking visibility: $e');
      print('$TAG: Stack trace: $stacktrace');
      return _isVisible;
    }
  }

  // Change the floating window icon (desktop only)
  Future<bool> changeFloatingIcon(String iconName) async {
    print('$TAG: Changing floating icon to: $iconName');
    try {
      if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        final result = await DesktopFloatingWindow.changeFloatingIcon(iconName);
        print('$TAG: Change icon result: $result');
        return result;
      }
      print('$TAG: Changing icon not supported on this platform');
      return false;
    } catch (e, stacktrace) {
      print('$TAG: Error changing floating icon: $e');
      print('$TAG: Stack trace: $stacktrace');
      return false;
    }
  }
}
