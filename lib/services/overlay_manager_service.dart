import 'package:flutter/material.dart';
import '../components/text_recognition_overlay.dart';

/// 覆盖层管理服务
/// 用于管理屏幕上的文本识别覆盖层
class OverlayManagerService {
  static final OverlayManagerService _instance = OverlayManagerService._internal();

  factory OverlayManagerService() {
    return _instance;
  }

  OverlayManagerService._internal();

  OverlayEntry? _overlayEntry;
  bool _isVisible = false;

  bool get isVisible => _isVisible;

  /// 显示文本识别覆盖层
  void showTextRecognitionOverlay(
    BuildContext context, {
    required String originalText,
    required String translatedText,
    required Rect boundingBox,
  }) {
    // 如果已经有覆盖层，先移除
    if (_isVisible) {
      removeOverlay();
    }

    // 获取Overlay状态
    final overlay = Overlay.of(context);

    // 创建覆盖层
    _overlayEntry = OverlayEntry(
      builder: (context) => TextRecognitionOverlay(
        originalText: originalText,
        translatedText: translatedText,
        boundingBox: boundingBox,
        onClose: () {
          removeOverlay();
        },
      ),
    );

    // 添加覆盖层
    overlay.insert(_overlayEntry!);
    _isVisible = true;
  }

  /// 移除覆盖层
  void removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isVisible = false;
    }
  }

  /// 切换覆盖层显示状态
  void toggleOverlay(
    BuildContext context, {
    required String originalText,
    required String translatedText,
    required Rect boundingBox,
  }) {
    if (_isVisible) {
      removeOverlay();
    } else {
      showTextRecognitionOverlay(
        context,
        originalText: originalText,
        translatedText: translatedText,
        boundingBox: boundingBox,
      );
    }
  }
}
