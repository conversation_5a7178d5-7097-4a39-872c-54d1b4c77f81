import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/translation_service.dart';
import '../components/translation_overlay_manager.dart';
import '../services/overlay_manager_service.dart';

class FloatingTranslatorService {
  static FloatingTranslatorService? _instance;

  // Private constructor
  FloatingTranslatorService._();

  // Singleton instance
  static FloatingTranslatorService get instance {
    _instance ??= FloatingTranslatorService._();
    return _instance!;
  }

  bool _isInitialized = false;
  final MethodChannel _floatingChannel = const MethodChannel(
    'app/floating_window',
  );
  final MethodChannel _screenCaptureChannel = const MethodChannel(
    'app/screen_capture',
  );

  // 翻译覆盖层管理器
  final TranslationOverlayManager _overlayManager = TranslationOverlayManager();

  // 文本识别覆盖层管理器
  final OverlayManagerService _textOverlayManager = OverlayManagerService();

  // 保存当前上下文，用于显示覆盖层
  BuildContext? _currentContext;

  // 保存最后识别的文本
  String? _lastRecognizedText;

  // 屏幕OCR识别结果监听器
  final StreamController<String> _recognizedTextController =
      StreamController<String>.broadcast();
  Stream<String> get onRecognizedText => _recognizedTextController.stream;

  // 错误信息监听器
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();
  Stream<String> get onError => _errorController.stream;

  // 权限状态变更监听器
  final StreamController<bool> _permissionController =
      StreamController<bool>.broadcast();
  Stream<bool> get onPermissionChanged => _permissionController.stream;

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    // 设置方法回调处理器
    _screenCaptureChannel.setMethodCallHandler(_handleMethodCall);

    _isInitialized = true;
    debugPrint('FloatingTranslatorService initialized');
  }

  // Handle method calls from native code
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    debugPrint('Received method call: ${call.method}');

    switch (call.method) {
      case 'translateText':
        final text = call.arguments as String;
        return await translateText(text);

      case 'onTextRecognized':
        try {
          // 这里接收从原生代码识别的文本
          final text = call.arguments as String;
          debugPrint(
            'Text recognized from native: ${text.length > 50 ? text.substring(0, 50) + "..." : text}',
          );

          // 保存最后识别的文本
          _lastRecognizedText = text;

          // 将识别的文本通过Stream发送给监听者
          _recognizedTextController.add(text);

          // 如果有上下文，则显示翻译覆盖层
          if (_currentContext != null) {
            // 使用Future.microtask确保UI线程不被阻塞
            Future.microtask(() async {
              await _showTranslationOverlay(text);
            });
          } else {
            debugPrint('No context available, cannot show overlay');
          }
        } catch (e, stackTrace) {
          debugPrint('Error handling text recognition: $e');
          debugPrint('Stack trace: $stackTrace');
          _errorController.add('Error handling text recognition: $e');
        }
        return true;

      case 'onPermissionResult':
        // 处理权限结果回调
        final granted = call.arguments as bool;
        debugPrint('Screen capture permission result: $granted');
        // 通知权限状态变更
        _permissionController.add(granted);
        return true;

      case 'debugMessage':
        // 接收调试消息
        final message = call.arguments as String;
        debugPrint('Native debug: $message');
        return true;

      default:
        return null;
    }
  }

  // 显示翻译覆盖层
  Future<void> _showTranslationOverlay(String originalText) async {
    if (_currentContext == null) return;

    try {
      // 翻译文本
      final translatedText = await translateText(originalText);
      debugPrint('Translated text: $translatedText');

      // 获取文本位置信息
      debugPrint('Capturing screen with bounds...');
      final textBounds = await forceCaptureScreenWithBounds();
      debugPrint('Captured screen with bounds: ${textBounds?.length ?? 0} text blocks');

      if (textBounds != null && textBounds.isNotEmpty) {
        // 找到与原文最匹配的文本块
        String bestMatchText = '';
        Rect bestMatchRect = Rect.zero;
        double bestMatchScore = 0;

        textBounds.forEach((text, rect) {
          // 计算文本相似度（简单实现，可以改进）
          final similarity = _calculateSimilarity(text, originalText);
          debugPrint('Comparing: "$text" with "$originalText", similarity: $similarity');
          if (similarity > bestMatchScore) {
            bestMatchScore = similarity;
            bestMatchText = text;
            bestMatchRect = rect as Rect;
          }
        });

        debugPrint('Best match: "$bestMatchText" with score: $bestMatchScore');
        if (bestMatchScore > 0.3) { // 降低相似度阈值，提高匹配成功率
          debugPrint('Using matched position: $bestMatchRect');

          // 使用系统级悬浮窗显示翻译结果
          await _showNativeTranslationOverlay(
            translatedText,
            bestMatchRect.left.toInt(),
            bestMatchRect.top.toInt(),
            bestMatchRect.width.toInt(),
            bestMatchRect.height.toInt(),
          );
          return;
        }
      }

      // 如果没有找到匹配的位置，使用屏幕中心位置
      debugPrint('No matching position found, using default position');
      final screenSize = MediaQuery.of(_currentContext!).size;
      final defaultBoundingBox = Rect.fromCenter(
        center: Offset(screenSize.width / 2, screenSize.height / 2),
        width: screenSize.width * 0.8,
        height: 100,
      );

      // 使用系统级悬浮窗显示翻译结果
      await _showNativeTranslationOverlay(
        translatedText,
        defaultBoundingBox.left.toInt(),
        defaultBoundingBox.top.toInt(),
        defaultBoundingBox.width.toInt(),
        defaultBoundingBox.height.toInt(),
      );
    } catch (e, stackTrace) {
      debugPrint('Error showing translation overlay: $e');
      debugPrint('Stack trace: $stackTrace');
      _errorController.add('翻译失败: $e');
    }
  }

  // 使用系统级悬浮窗显示翻译结果
  Future<void> _showNativeTranslationOverlay(
    String translatedText,
    int x,
    int y,
    int width,
    int height,
  ) async {
    try {
      debugPrint('Showing native translation overlay at x=$x, y=$y, width=$width, height=$height');

      // 调用原生方法显示翻译结果
      final result = await _screenCaptureChannel.invokeMethod<bool>(
        'showTranslationOverlay',
        {
          'originalText': _lastRecognizedText ?? '无原文',
          'translatedText': translatedText,
          'x': x,
          'y': y,
          'width': width,
          'height': height,
        },
      );

      if (result == true) {
        debugPrint('Native translation overlay shown successfully');
      } else {
        debugPrint('Failed to show native translation overlay');
        _errorController.add('显示翻译结果失败');
      }
    } catch (e) {
      debugPrint('Error showing native translation overlay: $e');
      _errorController.add('显示翻译结果失败: $e');
    }
  }

  // 计算两个字符串的相似度（0-1之间，1表示完全相同）
  double _calculateSimilarity(String a, String b) {
    if (a.isEmpty || b.isEmpty) return 0;

    // 将字符串转换为小写并去除空格，以便更好地匹配
    final normalizedA = a.toLowerCase().trim();
    final normalizedB = b.toLowerCase().trim();

    // 完全相同
    if (normalizedA == normalizedB) {
      return 1.0;
    }

    // 如果其中一个字符串包含另一个，给予较高的相似度
    if (normalizedA.contains(normalizedB)) {
      return 0.8 * (normalizedB.length / normalizedA.length);
    }
    if (normalizedB.contains(normalizedA)) {
      return 0.8 * (normalizedA.length / normalizedB.length);
    }

    // 计算共同单词数量
    final wordsA = normalizedA.split(RegExp(r'\s+'));
    final wordsB = normalizedB.split(RegExp(r'\s+'));

    int commonWords = 0;
    for (final wordA in wordsA) {
      if (wordA.length < 2) continue; // 忽略太短的单词
      if (wordsB.any((wordB) => wordB.contains(wordA) || wordA.contains(wordB))) {
        commonWords++;
      }
    }

    if (wordsA.isNotEmpty && commonWords > 0) {
      final wordSimilarity = commonWords / wordsA.length;
      if (wordSimilarity > 0.3) {
        return 0.5 + (wordSimilarity * 0.5); // 0.5-1.0之间的值
      }
    }

    // 计算字符匹配度
    final int maxLength = normalizedA.length > normalizedB.length ? normalizedA.length : normalizedB.length;
    final int minLength = normalizedA.length < normalizedB.length ? normalizedA.length : normalizedB.length;

    int matchCount = 0;
    for (int i = 0; i < minLength; i++) {
      if (normalizedA[i] == normalizedB[i]) {
        matchCount++;
      }
    }

    final charSimilarity = matchCount / maxLength;

    // 返回最大相似度
    return charSimilarity;
  }

  // Check if floating window permission is granted
  Future<bool> checkPermission() async {
    try {
      final bool hasPermission =
          await _floatingChannel.invokeMethod('checkPermission') ?? false;
      return hasPermission;
    } catch (e) {
      debugPrint('Error checking floating window permission: $e');
      return false;
    }
  }

  // Request floating window permission
  Future<bool> requestPermission() async {
    try {
      final bool granted =
          await _floatingChannel.invokeMethod('requestPermission') ?? false;
      return granted;
    } catch (e) {
      debugPrint('Error requesting floating window permission: $e');
      return false;
    }
  }

  // Show the floating translator window
  Future<bool> showFloatingTranslator(BuildContext context) async {
    try {
      // 保存上下文，用于后续显示覆盖层
      _currentContext = context;

      final result = await _floatingChannel.invokeMethod('show') ?? false;
      if (result) {
        // Start background service to maintain functionality when app is in background
        await _startBackgroundService();
      }
      return result;
    } catch (e) {
      debugPrint('Error showing floating translator: $e');
      return false;
    }
  }

  // Start background service (platform-specific implementation)
  Future<void> _startBackgroundService() async {
    try {
      await _screenCaptureChannel.invokeMethod('startService');
    } catch (e) {
      debugPrint('Error starting background service: $e');
    }
  }

  // Hide the floating translator window
  Future<bool> hideFloatingTranslator() async {
    try {
      final result = await _floatingChannel.invokeMethod('hide') ?? false;
      if (result) {
        // Stop background service when floating window is hidden
        await _stopBackgroundService();
      }
      return result;
    } catch (e) {
      debugPrint('Error hiding floating translator: $e');
      return false;
    }
  }

  // Stop background service (platform-specific implementation)
  Future<void> _stopBackgroundService() async {
    try {
      await _screenCaptureChannel.invokeMethod('stopService');
    } catch (e) {
      debugPrint('Error stopping background service: $e');
    }
  }

  // Translate text
  Future<String> translateText(String text) async {
    debugPrint(
      'Translating text: ${text.length > 50 ? text.substring(0, 50) + "..." : text}',
    );
    final targetLanguage = TranslationService.getDeviceLanguage();
    return await TranslationService.translateText(text, targetLanguage);
  }

  // Get text from clipboard
  Future<String?> getClipboardText() async {
    final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
    return data?.text;
  }

  // Copy text to clipboard
  Future<void> copyToClipboard(String text) async {
    await Clipboard.setData(ClipboardData(text: text));
  }

  // 强制触发屏幕捕获并返回识别的文本
  Future<String?> forceCaptureScreen() async {
    try {
      final Completer<String?> completer = Completer<String?>();

      // 添加一次性监听器，接收识别结果
      StreamSubscription? subscription;
      subscription = onRecognizedText.listen((text) {
        if (!completer.isCompleted) {
          completer.complete(text);
          subscription?.cancel();
        }
      });

      // 设置超时
      Future.delayed(const Duration(seconds: 10), () {
        if (!completer.isCompleted) {
          completer.complete(null);
          subscription?.cancel();
          _errorController.add('Screen capture timed out');
        }
      });

      // 调用原生方法捕获屏幕
      final result = await _screenCaptureChannel.invokeMethod<String>(
        'forceCaptureScreen',
      );

      // 如果原生方法直接返回了结果
      if (result != null && !completer.isCompleted) {
        subscription?.cancel();
        return result;
      }

      // 等待通过Stream接收到的结果
      return await completer.future;
    } catch (e) {
      debugPrint('Error forcing screen capture: $e');
      _errorController.add('Error capturing screen: $e');
      return null;
    }
  }

  // 强制触发屏幕捕获并返回识别的文本和位置信息
  Future<Map<String, dynamic>?> forceCaptureScreenWithBounds() async {
    try {
      debugPrint('Starting screen capture with bounds...');

      // 调用原生方法捕获屏幕并获取位置信息
      final result = await _screenCaptureChannel.invokeMethod<Map<dynamic, dynamic>>(
        'captureScreenWithBounds',
      );

      if (result != null) {
        debugPrint('Received result from native: ${result.length} items');

        // 将原生返回的Map转换为Dart Map
        final Map<String, dynamic> textBounds = {};
        result.forEach((key, value) {
          if (key is String && value is List) {
            debugPrint('Processing text: "$key" with bounds: $value');
            try {
              // 将位置信息转换为Rect
              final List<double> bounds = (value as List).map((e) => (e as num).toDouble()).toList();
              if (bounds.length == 4) {
                textBounds[key] = Rect.fromLTRB(
                  bounds[0], bounds[1], bounds[2], bounds[3],
                );
                debugPrint('Added text with bounds: $key -> ${textBounds[key]}');
              } else {
                debugPrint('Invalid bounds length: ${bounds.length}, expected 4');
              }
            } catch (e) {
              debugPrint('Error processing bounds for "$key": $e');
            }
          } else {
            debugPrint('Skipping invalid entry: key type: ${key.runtimeType}, value type: ${value.runtimeType}');
          }
        });

        debugPrint('Processed ${textBounds.length} text bounds');
        return textBounds;
      } else {
        debugPrint('No result received from native method');
      }

      return null;
    } catch (e, stackTrace) {
      debugPrint('Error forcing screen capture with bounds: $e');
      debugPrint('Stack trace: $stackTrace');
      _errorController.add('Error capturing screen with bounds: $e');
      return null;
    }
  }

  // 释放资源
  void dispose() {
    _recognizedTextController.close();
    _errorController.close();
    _permissionController.close();
  }
}
