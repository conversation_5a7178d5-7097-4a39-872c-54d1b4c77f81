import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../components/floating_action_widget.dart';

class FloatingWidgetDemoPage extends StatelessWidget {
  const FloatingWidgetDemoPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) return;
        context.go('/');
      },
      child: Scaffold(
        appBar: AppBar(
        title: const Text('Draggable Widget Demo'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/'),
        ),
      ),
      body: Stack(
        fit: StackFit.expand,
        children: [
          // Main content
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('拖动蓝色图标', style: TextStyle(fontSize: 20)),
                const SizedBox(height: 16),
                const Text(
                  '释放后会自动吸附到左/右边缘',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
                const SizedBox(height: 32),
                ElevatedButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('点击了按钮！'),
                        duration: Duration(milliseconds: 800),
                      ),
                    );
                  },
                  child: const Text('测试按钮'),
                ),
              ],
            ),
          ),

          // Floating widget that can be dragged
          FloatingActionWidget(
            icon: const Icon(Icons.touch_app, color: Colors.white),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('点击了悬浮按钮！'),
                  duration: Duration(milliseconds: 800),
                ),
              );
            },
          ),
        ],
      ),
      ),
    );
  }
}
