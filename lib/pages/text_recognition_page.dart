import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../components/floating_text_recognizer.dart';
import '../components/text_overlay.dart';

class TextRecognitionPage extends StatefulWidget {
  const TextRecognitionPage({Key? key}) : super(key: key);

  @override
  State<TextRecognitionPage> createState() => _TextRecognitionPageState();
}

class _TextRecognitionPageState extends State<TextRecognitionPage> {
  final List<RecognizedTextItem> _recognizedTexts = [];

  void _onTextRecognized(String text, Rect bounds) {
    setState(() {
      _recognizedTexts.add(RecognizedTextItem(text: text, bounds: bounds));
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) return;
        context.go('/');
      },
      child: Scaffold(
        appBar: AppBar(
        title: const Text('Text Recognition'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              setState(() {
                _recognizedTexts.clear();
              });
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          FloatingTextRecognizer(
            onTextRecognized: _onTextRecognized,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('示例文本', style: TextStyle(fontSize: 24)),
                    const SizedBox(height: 16),
                    const Text(
                      '这是一段示例文本，用于演示文本识别功能。\n'
                      '点击页面任意位置开始识别文本。\n'
                      '识别后的文本将会以半透明的方式覆盖在原文上方。',
                      style: TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
          ),
          ..._recognizedTexts.map(
            (item) => TextOverlay(
              text: item.text,
              bounds: item.bounds,
              onTap: () {
                setState(() {
                  _recognizedTexts.remove(item);
                });
              },
            ),
          ),
        ],
      ),
      ),
    );
  }
}

class RecognizedTextItem {
  final String text;
  final Rect bounds;

  RecognizedTextItem({required this.text, required this.bounds});
}
