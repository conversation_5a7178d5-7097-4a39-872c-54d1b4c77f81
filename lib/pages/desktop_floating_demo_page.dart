import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:desktop_floating_window/desktop_floating_window.dart';

class DesktopFloatingDemoPage extends StatefulWidget {
  const DesktopFloatingDemoPage({Key? key}) : super(key: key);

  @override
  State<DesktopFloatingDemoPage> createState() =>
      _DesktopFloatingDemoPageState();
}

class _DesktopFloatingDemoPageState extends State<DesktopFloatingDemoPage> {
  bool _hasPermission = false;
  bool _isFloatingVisible = false;
  StreamSubscription? _clickSubscription;
  String _lastEvent = "无事件";

  @override
  void initState() {
    super.initState();
    _checkPermission();
    _listenToClickEvents();
  }

  @override
  void dispose() {
    _clickSubscription?.cancel();
    super.dispose();
  }

  // 检查权限
  Future<void> _checkPermission() async {
    final hasPermission = await DesktopFloatingWindow.checkPermission();
    setState(() {
      _hasPermission = hasPermission;
    });
  }

  // 请求权限
  Future<void> _requestPermission() async {
    final granted = await DesktopFloatingWindow.requestPermission();
    setState(() {
      _hasPermission = granted;
    });

    if (granted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('悬浮窗权限已授予')));
    } else {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('悬浮窗权限被拒绝')));
    }
  }

  // 显示悬浮窗
  Future<void> _showFloatingWindow() async {
    if (!_hasPermission) {
      await _requestPermission();
      if (!_hasPermission) return;
    }

    final result = await DesktopFloatingWindow.showFloatingWindow();
    if (result) {
      setState(() {
        _isFloatingVisible = true;
      });
    }
  }

  // 隐藏悬浮窗
  Future<void> _hideFloatingWindow() async {
    final result = await DesktopFloatingWindow.hideFloatingWindow();
    if (result) {
      setState(() {
        _isFloatingVisible = false;
      });
    }
  }

  // 监听点击事件
  void _listenToClickEvents() {
    _clickSubscription = DesktopFloatingWindow.onFloatingWindowClick.listen((
      event,
    ) {
      setState(() {
        _lastEvent = "收到点击事件: $event ${DateTime.now().toString()}";
      });

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('悬浮窗被点击了！ $event')));
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) return;
        context.go('/');
      },
      child: Scaffold(
        appBar: AppBar(
        title: const Text('桌面悬浮窗演示'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/'),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '悬浮窗权限状态：${_hasPermission ? "已授权" : "未授权"}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '悬浮窗显示状态：${_isFloatingVisible ? "已显示" : "已隐藏"}',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '最近事件：$_lastEvent',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            if (!_hasPermission)
              ElevatedButton(
                onPressed: _requestPermission,
                child: const Text('请求悬浮窗权限'),
              ),
            const SizedBox(height: 16),
            if (_hasPermission && !_isFloatingVisible)
              ElevatedButton(
                onPressed: _showFloatingWindow,
                child: const Text('显示桌面悬浮窗'),
              ),
            if (_hasPermission && _isFloatingVisible)
              ElevatedButton(
                onPressed: _hideFloatingWindow,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.redAccent,
                ),
                child: const Text('隐藏桌面悬浮窗'),
              ),
            const SizedBox(height: 32),
            const Text(
              '说明：\n1. 悬浮窗可以在桌面上自由拖动\n2. 释放后会自动吸附到屏幕边缘\n3. 点击悬浮窗可以触发事件\n4. 即使退出应用，悬浮窗仍然可见',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
      ),
    );
  }
}
