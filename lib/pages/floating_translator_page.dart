import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import '../services/floating_window_service.dart';
import '../services/floating_translator_service.dart';
import '../services/overlay_manager_service.dart';
import '../utils/debug_logger.dart';
import 'package:flutter/rendering.dart';

class FloatingTranslatorPage extends StatefulWidget {
  const FloatingTranslatorPage({Key? key}) : super(key: key);

  @override
  State<FloatingTranslatorPage> createState() => _FloatingTranslatorPageState();
}

class _FloatingTranslatorPageState extends State<FloatingTranslatorPage> {
  final FloatingWindowService _floatingWindowService =
      FloatingWindowService.instance;
  final FloatingTranslatorService _translatorService =
      FloatingTranslatorService.instance;
  final DebugLogger _logger = DebugLogger('FloatingTranslatorPage');

  bool _hasPermission = false;
  bool _isWindowVisible = false;
  StreamSubscription? _eventSubscription;
  StreamSubscription? _permissionSubscription;
  bool _hasScreenCapturePermission = false;
  bool _isLoading = false;
  String? _lastCapturedText;

  // 用于日志显示
  final List<String> _debugLogs = [];
  final ScrollController _logScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    _setLoading(true);
    _addDebugLog('初始化服务...');
    _logger.log('Initializing services');

    try {
      // 初始化悬浮窗服务
      await _floatingWindowService.initialize();
      _logger.log('FloatingWindowService initialized');
      _addDebugLog('悬浮窗服务初始化完成');

      // 初始化翻译服务
      await _translatorService.initialize();
      _logger.log('FloatingTranslatorService initialized');
      _addDebugLog('翻译服务初始化完成');

      // 检查权限
      final hasPermission = await _floatingWindowService.checkPermission();
      _logger.log('Permission check result: $hasPermission');
      _addDebugLog('悬浮窗权限状态: $hasPermission');

      // 检查窗口是否可见
      final isWindowVisible =
          await _floatingWindowService.isFloatingWindowVisible();
      _logger.log('Window visibility check: $isWindowVisible');
      _addDebugLog('悬浮窗显示状态: $isWindowVisible');

      // 检查屏幕捕获权限
      await _checkScreenCapturePermission();

      // 监听悬浮窗事件
      _eventSubscription = _floatingWindowService.onFloatingWindowEvent.listen(
        _handleWindowEvent,
      );
      _logger.log('Window event subscription set up');
      _addDebugLog('悬浮窗事件监听已设置');

      // 监听屏幕捕获权限变更
      _permissionSubscription = _translatorService.onPermissionChanged.listen(
        _handlePermissionChanged,
      );
      _logger.log('Permission event subscription set up');
      _addDebugLog('权限事件监听已设置');

      if (mounted) {
        setState(() {
          _hasPermission = hasPermission;
          _isWindowVisible = isWindowVisible;
        });
      }
    } catch (e, stackTrace) {
      _logger.log('Error initializing services: $e', isError: true);
      _addDebugLog('初始化服务出错: $e');
      _addDebugLog('错误堆栈: $stackTrace');
    } finally {
      _setLoading(false);
    }
  }

  void _handleWindowEvent(String event) {
    _logger.log('Received window event: $event');
    _addDebugLog('收到悬浮窗事件: $event');

    if (event.startsWith('permission:')) {
      final hasPermission = event.split(':')[1] == 'true';
      _logger.log('Window permission changed: $hasPermission');
      _addDebugLog('悬浮窗权限状态变更: $hasPermission');

      if (mounted) {
        setState(() {
          _hasPermission = hasPermission;
        });
      }
    } else if (event == 'click') {
      _logger.log('Window was clicked');
      _addDebugLog('悬浮窗被点击，触发屏幕捕获');
      // 处理点击事件，可以在这里调用屏幕捕获逻辑
      _forceCaptureScreen();
    }
  }

  // 处理屏幕捕获权限变更
  void _handlePermissionChanged(bool granted) {
    _logger.log('Screen capture permission changed: $granted');
    _addDebugLog('屏幕捕获权限状态变更: $granted');

    if (mounted) {
      setState(() {
        _hasScreenCapturePermission = granted;
      });

      if (granted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('屏幕捕获权限已授予')),
        );
      }
    }
  }

  // 请求悬浮窗权限
  Future<void> _requestPermission() async {
    _setLoading(true);
    _logger.log('Requesting permission');
    _addDebugLog('正在请求悬浮窗权限...');

    try {
      final result = await _floatingWindowService.requestPermission();
      _logger.log('Permission request result: $result');
      _addDebugLog('悬浮窗权限请求结果: $result');

      if (mounted) {
        setState(() {
          _hasPermission = result;
        });
      }

      if (result) {
        // 如果悬浮窗权限已授予，继续请求屏幕捕获权限
        await _requestScreenCapturePermission();

        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('悬浮窗权限已授予')));

        // 再次检查权限状态
        await _refreshPermissionStatus();
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('悬浮窗权限被拒绝')));
      }
    } catch (e, stackTrace) {
      _logger.log('Error requesting permission: $e', isError: true);
      _addDebugLog('请求权限出错: $e');
      _addDebugLog('错误堆栈: $stackTrace');
    } finally {
      _setLoading(false);
    }
  }

  // 刷新权限状态
  Future<void> _refreshPermissionStatus() async {
    _addDebugLog('刷新权限状态...');
    try {
      final hasPermission = await _floatingWindowService.checkPermission();
      final isWindowVisible =
          await _floatingWindowService.isFloatingWindowVisible();

      if (mounted) {
        setState(() {
          _hasPermission = hasPermission;
          _isWindowVisible = isWindowVisible;
        });
      }

      _addDebugLog('悬浮窗权限状态: $_hasPermission');
      _addDebugLog('悬浮窗显示状态: $_isWindowVisible');
    } catch (e) {
      _addDebugLog('刷新权限状态出错: $e');
    }
  }

  // 切换悬浮窗显示状态
  Future<void> _toggleFloatingWindow() async {
    if (_isWindowVisible) {
      await _hideFloatingWindow();
    } else {
      await _showFloatingWindow();
    }
  }

  // 显示悬浮窗
  Future<void> _showFloatingWindow() async {
    _setLoading(true);
    _logger.log('Showing floating window');
    _addDebugLog('正在显示屏幕翻译悬浮窗...');

    try {
      if (!_hasPermission) {
        _addDebugLog('没有悬浮窗权限，先请求权限');
        await _requestPermission();
        if (!_hasPermission) {
          _addDebugLog('权限请求失败，无法显示悬浮窗');
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('无法显示悬浮窗：没有悬浮窗权限')));
          return;
        }
      }

      // 使用FloatingTranslatorService显示悬浮窗
      final result = await _translatorService.showFloatingTranslator(context);
      _logger.log('Show result: $result');
      _addDebugLog('悬浮窗显示结果: $result');

      // 延迟一下再检查状态，让服务有时间启动
      await Future.delayed(const Duration(milliseconds: 500));
      await _refreshPermissionStatus();

      if (mounted && result) {
        setState(() {
          _isWindowVisible = true;
        });

        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('屏幕翻译悬浮窗已显示，点击悬浮窗可识别并翻译屏幕文本')));
      } else {
        _addDebugLog('显示失败，但方法返回成功。检查Android日志获取更多信息。');
      }
    } catch (e, stackTrace) {
      _logger.log('Error showing window: $e', isError: true);
      _addDebugLog('显示悬浮窗出错: $e');
      _addDebugLog('错误堆栈: $stackTrace');
    } finally {
      _setLoading(false);
    }
  }

  // 隐藏悬浮窗
  Future<void> _hideFloatingWindow() async {
    _setLoading(true);
    _logger.log('Hiding floating window');
    _addDebugLog('正在隐藏屏幕翻译悬浮窗...');

    try {
      final result = await _floatingWindowService.hideFloatingWindow();
      _logger.log('Hide result: $result');
      _addDebugLog('悬浮窗隐藏结果: $result');

      // 延迟一下再检查状态
      await Future.delayed(const Duration(milliseconds: 500));
      await _refreshPermissionStatus();

      if (mounted && result) {
        setState(() {
          _isWindowVisible = false;
        });

        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('屏幕翻译悬浮窗已隐藏')));
      }
    } catch (e, stackTrace) {
      _logger.log('Error hiding window: $e', isError: true);
      _addDebugLog('隐藏悬浮窗出错: $e');
      _addDebugLog('错误堆栈: $stackTrace');
    } finally {
      _setLoading(false);
    }
  }

  // 检查屏幕捕获权限
  Future<void> _checkScreenCapturePermission() async {
    _addDebugLog('正在检查屏幕捕获权限...');
    try {
      const platform = MethodChannel('app/screen_capture');
      final hasPermission = await platform.invokeMethod('hasPermission');
      setState(() {
        _hasScreenCapturePermission = hasPermission ?? false;
      });
      _addDebugLog('屏幕捕获权限状态: $_hasScreenCapturePermission');
    } catch (e) {
      _addDebugLog('检查屏幕捕获权限出错: $e');
    }
  }

  // 请求屏幕捕获权限
  Future<void> _requestScreenCapturePermission() async {
    _addDebugLog('正在请求屏幕捕获权限...');
    try {
      const platform = MethodChannel('app/screen_capture');
      await platform.invokeMethod('requestPermission');
      _addDebugLog('屏幕捕获权限请求已发送');

      // 注意：权限结果将通过 onPermissionChanged 流接收
      // 不需要在这里检查权限状态，因为我们已经设置了监听器
    } catch (e) {
      _addDebugLog('请求屏幕捕获权限出错: $e');
    }
  }

  // 强制捕获屏幕
  Future<void> _forceCaptureScreen() async {
    _setLoading(true);
    _addDebugLog('尝试强制捕获屏幕...');
    try {
      const platform = MethodChannel('app/screen_capture');
      final result = await platform.invokeMethod('forceCaptureScreen');
      if (result != null) {
        final text = result.toString();
        setState(() {
          _lastCapturedText = text;
        });

        // 打印到控制台
        print('========== 屏幕捕获文本内容 ==========');
        print(text);
        print('===================================');

        // 日志显示
        _addDebugLog('捕获成功，检测到文本:');
        // 将文本分割成多行，每行显示一条日志
        final lines = text.split('\n');
        for (var i = 0; i < lines.length; i++) {
          if (lines[i].trim().isNotEmpty) {
            _addDebugLog('${i + 1}: ${lines[i]}');
          }
        }
      } else {
        setState(() {
          _lastCapturedText = null;
        });
        _addDebugLog('捕获成功，但未检测到文本');
        print('========== 屏幕捕获：未检测到文本 ==========');
      }
    } catch (e) {
      setState(() {
        _lastCapturedText = null;
      });
      _addDebugLog('捕获屏幕失败: $e');
      print('========== 屏幕捕获失败 ==========');
      print(e);
      print('===================================');
    } finally {
      _setLoading(false);
    }
  }

  // 测试覆盖层显示
  Future<void> _testOverlayDisplay() async {
    _setLoading(true);
    _addDebugLog('测试系统级悬浮窗显示...');

    try {
      // 先捕获屏幕文本
      if (_lastCapturedText == null || _lastCapturedText!.isEmpty) {
        _addDebugLog('先捕获屏幕文本...');
        await _forceCaptureScreen();
      }

      if (_lastCapturedText != null && _lastCapturedText!.isNotEmpty) {
        _addDebugLog('使用捕获的文本显示系统级悬浮窗');

        // 获取屏幕尺寸
        final screenSize = MediaQuery.of(context).size;

        // 翻译文本
        _addDebugLog('翻译文本...');
        final translatedText = await _translatorService.translateText(_lastCapturedText!);
        _addDebugLog('翻译结果: $translatedText');

        // 获取文本位置信息
        _addDebugLog('获取文本位置信息...');
        final textBounds = await _translatorService.forceCaptureScreenWithBounds();

        if (textBounds != null && textBounds.isNotEmpty) {
          _addDebugLog('找到 ${textBounds.length} 个文本块');

          // 找到与原文最匹配的文本块
          String bestMatchText = '';
          Rect bestMatchRect = Rect.zero;
          double bestMatchScore = 0;

          // 简单的相似度计算函数
          double calculateSimilarity(String a, String b) {
            if (a.isEmpty || b.isEmpty) return 0;
            if (a.toLowerCase().contains(b.toLowerCase())) return 0.8;
            if (b.toLowerCase().contains(a.toLowerCase())) return 0.8;
            return 0.0;
          }

          textBounds.forEach((text, rect) {
            final similarity = calculateSimilarity(text, _lastCapturedText!);
            _addDebugLog('比较: "$text" 与 "${_lastCapturedText}", 相似度: $similarity');
            if (similarity > bestMatchScore) {
              bestMatchScore = similarity;
              bestMatchText = text;
              bestMatchRect = rect as Rect;
            }
          });

          if (bestMatchScore > 0.3) {
            _addDebugLog('找到最佳匹配: "$bestMatchText", 位置: $bestMatchRect');

            // 调用原生方法显示翻译结果
            _addDebugLog('显示系统级悬浮窗...');
            final result = await const MethodChannel('app/screen_capture').invokeMethod<bool>(
              'showTranslationOverlay',
              {
                'originalText': _lastCapturedText,
                'translatedText': translatedText,
                'x': bestMatchRect.left.toInt(),
                'y': bestMatchRect.top.toInt(),
                'width': bestMatchRect.width.toInt(),
                'height': bestMatchRect.height.toInt(),
              },
            );

            if (result == true) {
              _addDebugLog('系统级悬浮窗显示成功');
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('系统级悬浮窗显示成功')),
              );
            } else {
              _addDebugLog('系统级悬浮窗显示失败');
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('系统级悬浮窗显示失败')),
              );
            }
            return;
          }
        }

        // 如果没有找到匹配的位置，使用屏幕中心位置
        _addDebugLog('没有找到匹配的位置，使用屏幕中心位置');
        final defaultBoundingBox = Rect.fromCenter(
          center: Offset(screenSize.width / 2, screenSize.height / 2),
          width: screenSize.width * 0.8,
          height: 100,
        );

        // 调用原生方法显示翻译结果
        _addDebugLog('显示系统级悬浮窗...');
        final result = await const MethodChannel('app/screen_capture').invokeMethod<bool>(
          'showTranslationOverlay',
          {
            'originalText': _lastCapturedText,
            'translatedText': translatedText,
            'x': defaultBoundingBox.left.toInt(),
            'y': defaultBoundingBox.top.toInt(),
            'width': defaultBoundingBox.width.toInt(),
            'height': defaultBoundingBox.height.toInt(),
          },
        );

        if (result == true) {
          _addDebugLog('系统级悬浮窗显示成功');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('系统级悬浮窗显示成功')),
          );
        } else {
          _addDebugLog('系统级悬浮窗显示失败');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('系统级悬浮窗显示失败')),
          );
        }
      } else {
        _addDebugLog('没有可用的文本，无法显示系统级悬浮窗');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('没有可用的文本，请先捕获屏幕')),
        );
      }
    } catch (e, stackTrace) {
      _addDebugLog('显示系统级悬浮窗失败: $e');
      _addDebugLog('错误堆栈: $stackTrace');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('显示系统级悬浮窗失败: $e')),
      );
    } finally {
      _setLoading(false);
    }
  }

  // 设置加载状态
  void _setLoading(bool loading) {
    if (mounted) {
      setState(() {
        _isLoading = loading;
      });
    }
  }

  // 添加调试日志
  void _addDebugLog(String log) {
    final timestamp = DateTime.now();
    final timeString =
        '${timestamp.hour}:${timestamp.minute}:${timestamp.second}.${timestamp.millisecond}';
    final logWithTime = '[$timeString] $log';

    setState(() {
      _debugLogs.add(logWithTime);
      // 限制日志数量，避免内存问题
      if (_debugLogs.length > 100) {
        _debugLogs.removeAt(0);
      }
    });

    // 滚动到最新日志
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_logScrollController.hasClients) {
        _logScrollController.animateTo(
          _logScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _eventSubscription?.cancel();
    _permissionSubscription?.cancel();
    _logScrollController.dispose();
    _logger.log('Disposing page');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (didPop) return;
        context.go('/');
      },
      child: Scaffold(
        appBar: AppBar(
        title: const Text('悬浮翻译'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/'),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshPermissionStatus,
            tooltip: '刷新权限状态',
          ),
        ],
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 权限状态卡片
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '权限状态',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '悬浮窗权限: ${_hasPermission ? "已授权" : "未授权"}',
                          style: TextStyle(
                            fontSize: 16,
                            color: _hasPermission ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '屏幕捕获权限: ${_hasScreenCapturePermission ? "已授权" : "未授权"}',
                          style: TextStyle(
                            fontSize: 16,
                            color:
                                _hasScreenCapturePermission
                                    ? Colors.green
                                    : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '悬浮窗显示状态: ${_isWindowVisible ? "已显示" : "已隐藏"}',
                          style: TextStyle(
                            fontSize: 16,
                            color:
                                _isWindowVisible ? Colors.green : Colors.orange,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            if (!_hasPermission)
                              ElevatedButton(
                                onPressed:
                                    _isLoading ? null : _requestPermission,
                                child: const Text('请求悬浮窗权限'),
                              ),
                            if (!_hasPermission) const SizedBox(width: 8),
                            if (!_hasScreenCapturePermission)
                              ElevatedButton(
                                onPressed:
                                    _isLoading
                                        ? null
                                        : _requestScreenCapturePermission,
                                child: const Text('请求屏幕捕获权限'),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // 控制按钮
                if (_hasPermission)
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '悬浮窗控制',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed:
                                _isLoading ? null : _toggleFloatingWindow,
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  _isWindowVisible ? Colors.red : Colors.blue,
                              foregroundColor: Colors.white,
                              minimumSize: const Size(double.infinity, 48),
                            ),
                            child: Text(_isWindowVisible ? '隐藏悬浮窗' : '显示悬浮窗'),
                          ),
                          if (_hasScreenCapturePermission)
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: ElevatedButton(
                                onPressed:
                                    _isLoading ? null : _forceCaptureScreen,
                                style: ElevatedButton.styleFrom(
                                  minimumSize: const Size(double.infinity, 48),
                                ),
                                child: const Text('测试屏幕捕获'),
                              ),
                            ),
                          if (_hasScreenCapturePermission)
                            Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: ElevatedButton(
                                onPressed: _isLoading ? null : _testOverlayDisplay,
                                style: ElevatedButton.styleFrom(
                                  minimumSize: const Size(double.infinity, 48),
                                  backgroundColor: Colors.green,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('测试原文位置显示翻译'),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),

                const SizedBox(height: 16),

                // 识别结果显示
                if (_lastCapturedText != null)
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '识别结果',
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              IconButton(
                                icon: const Icon(Icons.copy),
                                onPressed: () {
                                  if (_lastCapturedText != null) {
                                    Clipboard.setData(
                                      ClipboardData(text: _lastCapturedText!),
                                    );
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(content: Text('已复制到剪贴板')),
                                    );
                                  }
                                },
                                tooltip: '复制文本',
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            padding: const EdgeInsets.all(8),
                            constraints: const BoxConstraints(maxHeight: 150),
                            child: SingleChildScrollView(
                              child: Text(_lastCapturedText!),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                if (_lastCapturedText != null) const SizedBox(height: 16),

                // 调试日志
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '调试日志',
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              IconButton(
                                icon: const Icon(Icons.clear_all),
                                onPressed: () {
                                  setState(() {
                                    _debugLogs.clear();
                                  });
                                },
                                tooltip: '清除日志',
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(4),
                              ),
                              padding: const EdgeInsets.all(8),
                              child: ListView.builder(
                                controller: _logScrollController,
                                itemCount: _debugLogs.length,
                                itemBuilder: (context, index) {
                                  final log = _debugLogs[index];
                                  final isError =
                                      log.toLowerCase().contains('错误') ||
                                      log.toLowerCase().contains('error') ||
                                      log.toLowerCase().contains('失败') ||
                                      log.toLowerCase().contains('exception');
                                  return Text(
                                    log,
                                    style: TextStyle(
                                      fontFamily: 'monospace',
                                      fontSize: 12,
                                      color: isError ? Colors.red : null,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: const Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
      ),
    );
  }
}

// 辅助函数
int min(int a, int b) => a < b ? a : b;
