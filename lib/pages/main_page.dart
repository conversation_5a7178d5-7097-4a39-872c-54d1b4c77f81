import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../components/app_floating_widget.dart';
import 'home_page.dart';
import 'mine_page.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  int _selectedIndex = 0;
  final AppFloatingWidget _floatingWidget = AppFloatingWidget();

  final List<Widget> _screens = [const HomePage(), const MinePage()];

  @override
  void initState() {
    super.initState();
    // Set Android system navigation bar color to white
    if (Platform.isAndroid) {
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          systemNavigationBarColor:
              Colors.white, // Set navigation bar background color
          systemNavigationBarIconBrightness:
              Brightness.dark, // Navigation bar buttons dark color
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _floatingWidget.toggle(
            context,
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('悬浮窗被点击！'),
                  duration: Duration(seconds: 1),
                ),
              );
            },
            icon: const Icon(Icons.touch_app, color: Colors.white),
          );
        },
        child: Icon(_floatingWidget.isVisible ? Icons.close : Icons.add),
      ),
      bottomNavigationBar: Theme(
        data: Theme.of(context).copyWith(
          splashColor: Colors.blue.withOpacity(
            0.1,
          ), // Custom ripple effect color
          highlightColor: Colors.transparent, // Custom highlight color
        ),
        child: BottomNavigationBar(
          currentIndex: _selectedIndex,
          onTap: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
          items: const [
            BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
            BottomNavigationBarItem(
              icon: Icon(Icons.person),
              label: 'My Profile',
            ),
          ],
          selectedItemColor: Colors.blue,
          unselectedItemColor: Colors.grey,
          elevation: 8.0,
          type: BottomNavigationBarType.fixed,
        ),
      ),
    );
  }
}
