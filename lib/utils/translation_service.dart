import 'dart:io';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';

class TranslationService {
  static final Dio _dio = Dio();
  
  // 翻译文本方法
  static Future<String> translateText(String text, String targetLanguage) async {
    try {
      debugPrint('开始翻译文本，目标语言: $targetLanguage, 文本长度: ${text.length}');
      
      // 这里可以集成实际的翻译API
      // 下面是一个简单模拟，在实际应用中应该换成真实的API调用
      
      // 模拟网络延迟
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 根据检测到的语言执行简单翻译
      String translatedText;
      
      // 检测文本语言（简单逻辑，实际应用应使用语言检测API）
      bool isEnglish = _isEnglishText(text);
      bool isChinese = _isChineseText(text);
      
      if (isEnglish && targetLanguage == 'zh') {
        // 英文翻译成中文 - 模拟
        translatedText = _mockTranslateEnglishToChinese(text);
      } else if (isChinese && targetLanguage == 'en') {
        // 中文翻译成英文 - 模拟
        translatedText = _mockTranslateChineseToEnglish(text);
      } else {
        // 默认返回原文
        translatedText = "无法确定语言，无法翻译: $text";
      }
      
      return translatedText;
      
      // 实际API调用示例：
      /*
      final response = await _dio.post(
        'https://translation-api.example.com/translate',
        data: {
          'text': text,
          'target_language': targetLanguage,
        },
      );
      
      if (response.statusCode == 200) {
        return response.data['translated_text'];
      } else {
        throw Exception('Failed to translate text');
      }
      */
    } catch (e) {
      debugPrint('Translation error: $e');
      return '翻译失败: $e';
    }
  }
  
  // 检测是否为英文文本（简单检测）
  static bool _isEnglishText(String text) {
    // 简单检测，判断是否主要包含英文字符
    final englishRegex = RegExp(r'[a-zA-Z]');
    final englishCharCount = englishRegex.allMatches(text).length;
    return englishCharCount > text.length * 0.5;
  }
  
  // 检测是否为中文文本（简单检测）
  static bool _isChineseText(String text) {
    // 简单检测，判断是否主要包含中文字符
    final chineseRegex = RegExp(r'[\u4e00-\u9fa5]');
    final chineseCharCount = chineseRegex.allMatches(text).length;
    return chineseCharCount > text.length * 0.3;  // 中文通常间隔较多，所以阈值低一些
  }
  
  // 模拟英译中（仅用于演示）
  static String _mockTranslateEnglishToChinese(String text) {
    // 这里用一些简单的英文词汇进行替换
    // 实际应用中应该使用专业翻译API
    final Map<String, String> dictionary = {
      'hello': '你好',
      'world': '世界',
      'welcome': '欢迎',
      'thank you': '谢谢',
      'good': '好的',
      'morning': '早上',
      'afternoon': '下午',
      'evening': '晚上',
      'night': '晚上',
      'today': '今天',
      'tomorrow': '明天',
      'yesterday': '昨天',
      'apple': '苹果',
      'computer': '电脑',
      'phone': '手机',
    };
    
    String result = text;
    dictionary.forEach((eng, chn) {
      result = result.replaceAll(RegExp(eng, caseSensitive: false), chn);
    });
    
    return result;
  }
  
  // 模拟中译英（仅用于演示）
  static String _mockTranslateChineseToEnglish(String text) {
    // 这里用一些简单的中文词汇进行替换
    // 实际应用中应该使用专业翻译API
    final Map<String, String> dictionary = {
      '你好': 'hello',
      '世界': 'world',
      '欢迎': 'welcome',
      '谢谢': 'thank you',
      '好的': 'good',
      '早上': 'morning',
      '下午': 'afternoon',
      '晚上': 'evening',
      '今天': 'today',
      '明天': 'tomorrow',
      '昨天': 'yesterday',
      '苹果': 'apple',
      '电脑': 'computer',
      '手机': 'phone',
    };
    
    String result = text;
    dictionary.forEach((chn, eng) {
      result = result.replaceAll(chn, eng);
    });
    
    return result;
  }
  
  // 获取设备语言
  static String getDeviceLanguage() {
    final String locale = Platform.localeName;
    return locale.split('_')[0]; // 返回语言代码，如'en', 'zh'等
  }
}