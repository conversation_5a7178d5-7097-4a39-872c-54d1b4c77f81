import 'dart:developer' as developer;

/// 调试日志工具类，用于统一管理应用程序的日志信息
class DebugLogger {
  final String _tag;
  final List<LogEntry> _logs = [];

  /// 构造函数，需要提供一个标签用于识别日志来源
  DebugLogger(this._tag);

  /// 获取所有日志项
  List<LogEntry> get logs => List.unmodifiable(_logs);

  /// 记录一条日志
  /// [message] 日志消息
  /// [isError] 是否为错误日志
  void log(String message, {bool isError = false}) {
    final entry = LogEntry(
      tag: _tag,
      message: message,
      timestamp: DateTime.now(),
      isError: isError,
    );

    _logs.add(entry);

    if (isError) {
      developer.log('[$_tag] ERROR: $message', name: 'ERROR');
    } else {
      developer.log('[$_tag] $message', name: 'DEBUG');
    }
  }

  /// 清除所有日志
  void clear() {
    _logs.clear();
  }

  /// 保存日志到本地文件
  Future<bool> saveToFile() async {
    // 这里可以实现将日志保存到本地文件的逻辑
    // 例如使用 path_provider 和 dart:io 包
    return true;
  }

  /// 发送日志到服务器
  Future<bool> sendToServer() async {
    // 这里可以实现将日志发送到服务器的逻辑
    return true;
  }
}

/// 日志条目类，表示一条完整的日志记录
class LogEntry {
  final String tag;
  final String message;
  final DateTime timestamp;
  final bool isError;

  const LogEntry({
    required this.tag,
    required this.message,
    required this.timestamp,
    this.isError = false,
  });

  @override
  String toString() {
    final timeString =
        '${timestamp.hour}:${timestamp.minute}:${timestamp.second}.${timestamp.millisecond}';
    return '[$timeString][$tag] ${isError ? 'ERROR: ' : ''}$message';
  }
}
