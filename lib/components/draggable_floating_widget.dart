import 'package:flutter/material.dart';

class DraggableFloatingWidget extends StatefulWidget {
  final Widget child;
  final double width;
  final double height;
  final EdgeInsets padding;
  final Function? onTap;
  final bool stickToEdge;

  const DraggableFloatingWidget({
    Key? key,
    required this.child,
    this.width = 60.0,
    this.height = 60.0,
    this.padding = EdgeInsets.zero,
    this.onTap,
    this.stickToEdge = true,
  }) : super(key: key);

  @override
  State<DraggableFloatingWidget> createState() =>
      _DraggableFloatingWidgetState();
}

class _DraggableFloatingWidgetState extends State<DraggableFloatingWidget> {
  late double _x;
  late double _y;
  late double _screenWidth;
  late double _screenHeight;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    // Initialize position to center right of screen
    _x = 0;
    _y = 0;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateScreenSize();
      _x = _screenWidth - widget.width - 20;
      _y = _screenHeight / 2 - widget.height / 2;
      setState(() {});
    });
  }

  void _updateScreenSize() {
    final size = MediaQuery.of(context).size;
    _screenWidth = size.width;
    _screenHeight =
        size.height -
        MediaQuery.of(context).padding.top -
        MediaQuery.of(context).padding.bottom;
  }

  void _onDragEnd(DragEndDetails details) {
    if (widget.stickToEdge) {
      setState(() {
        _isDragging = false;
        // Stick to the nearest edge
        if (_x < _screenWidth / 2) {
          // Stick to left edge
          _x = 0;
        } else {
          // Stick to right edge
          _x = _screenWidth - widget.width;
        }
      });
    } else {
      setState(() {
        _isDragging = false;
      });
    }
  }

  void _onDragUpdate(DragUpdateDetails details) {
    setState(() {
      _isDragging = true;
      _x += details.delta.dx;
      _y += details.delta.dy;

      // Ensure the widget stays within the screen bounds
      _x = _x.clamp(0, _screenWidth - widget.width);
      _y = _y.clamp(0, _screenHeight - widget.height);
    });
  }

  @override
  Widget build(BuildContext context) {
    _updateScreenSize();

    return Positioned(
      left: _x,
      top: _y,
      child: GestureDetector(
        onPanUpdate: _onDragUpdate,
        onPanEnd: _onDragEnd,
        onTap: widget.onTap != null ? () => widget.onTap!() : null,
        child: Container(
          width: widget.width,
          height: widget.height,
          padding: widget.padding,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(_isDragging ? 0.8 : 0.6),
            borderRadius: BorderRadius.circular(widget.width / 2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: widget.child,
        ),
      ),
    );
  }
}
