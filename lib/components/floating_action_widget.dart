import 'package:flutter/material.dart';
import 'draggable_floating_widget.dart';

class FloatingActionWidget extends StatelessWidget {
  final Function()? onPressed;
  final Widget icon;
  final Color backgroundColor;
  final bool stickToEdge;

  const FloatingActionWidget({
    Key? key,
    this.onPressed,
    required this.icon,
    this.backgroundColor = Colors.blue,
    this.stickToEdge = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DraggableFloatingWidget(
      width: 56.0,
      height: 56.0,
      stickToEdge: stickToEdge,
      onTap: onPressed,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: Center(child: icon),
      ),
    );
  }
}
