import 'package:flutter/material.dart';
import '../services/floating_translator_service.dart';

/// 悬浮翻译器组件，用于在应用内部展示浮动翻译界面预览
class FloatingTranslator extends StatefulWidget {
  const FloatingTranslator({Key? key}) : super(key: key);

  @override
  State<FloatingTranslator> createState() => _FloatingTranslatorState();
}

class _FloatingTranslatorState extends State<FloatingTranslator> {
  bool _isExpanded = false;
  String _originalText = '点击按钮展开翻译面板\n然后点击屏幕任意位置捕获内容进行翻译';
  String _translatedText = '';
  bool _isLoading = false;
  String _errorMessage = '';

  final FloatingTranslatorService _translatorService =
      FloatingTranslatorService.instance;

  @override
  void initState() {
    super.initState();
    _setupListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }

  // 设置监听器
  void _setupListeners() {
    // 监听OCR识别结果
    _translatorService.onRecognizedText.listen((text) {
      _handleRecognizedText(text);
    });

    // 监听错误信息
    _translatorService.onError.listen((error) {
      setState(() {
        _errorMessage = error;
        _isLoading = false;
      });
    });
  }

  // 处理OCR识别结果
  void _handleRecognizedText(String text) async {
    setState(() {
      _originalText = text;
      _isLoading = true;
    });

    try {
      // 调用翻译服务
      final translated = await _translatorService.translateText(text);

      setState(() {
        _translatedText = translated;
        _isLoading = false;
        _errorMessage = '';
        _isExpanded = true; // 自动展开显示翻译结果
      });
    } catch (e) {
      setState(() {
        _translatedText = '翻译失败: $e';
        _isLoading = false;
        _errorMessage = '翻译出错: $e';
      });
    }
  }

  // 触发屏幕捕获
  Future<void> _captureScreen() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final text = await _translatorService.forceCaptureScreen();
      if (text != null && text.isNotEmpty) {
        _handleRecognizedText(text);
      } else {
        setState(() {
          _errorMessage = '未能识别到文本';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '屏幕捕获失败: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 300,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部控制栏
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '屏幕翻译',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: Icon(
                          Icons.camera_alt,
                          color: Colors.white,
                          size: 20,
                        ),
                        padding: EdgeInsets.zero,
                        constraints: BoxConstraints(),
                        onPressed: _captureScreen,
                        tooltip: '捕获屏幕',
                      ),
                      SizedBox(width: 12),
                      IconButton(
                        icon: Icon(
                          _isExpanded ? Icons.expand_less : Icons.expand_more,
                          color: Colors.white,
                        ),
                        padding: EdgeInsets.zero,
                        constraints: BoxConstraints(),
                        onPressed: () {
                          setState(() {
                            _isExpanded = !_isExpanded;
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // 加载指示器
            if (_isLoading) const LinearProgressIndicator(),

            // 错误信息
            if (_errorMessage.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                color: Colors.red.shade100,
                child: Text(
                  _errorMessage,
                  style: TextStyle(color: Colors.red.shade900, fontSize: 12),
                ),
              ),

            // 翻译内容区域
            AnimatedCrossFade(
              duration: const Duration(milliseconds: 300),
              crossFadeState:
                  _isExpanded
                      ? CrossFadeState.showSecond
                      : CrossFadeState.showFirst,
              firstChild: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: Text(
                  '点击展开查看翻译内容',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ),
              secondChild: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                constraints: const BoxConstraints(maxHeight: 300),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '原文:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(_originalText),
                      const SizedBox(height: 16),
                      Text(
                        '翻译:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _translatedText.isEmpty ? '等待翻译...' : _translatedText,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton.icon(
                            icon: const Icon(Icons.copy, size: 16),
                            label: const Text('复制'),
                            onPressed: () {
                              if (_translatedText.isNotEmpty) {
                                _translatorService.copyToClipboard(
                                  _translatedText,
                                );
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(content: Text('已复制到剪贴板')),
                                );
                              }
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
