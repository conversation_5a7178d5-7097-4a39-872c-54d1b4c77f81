import 'package:flutter/material.dart';
import 'translation_overlay.dart';

/// 翻译覆盖层管理器
/// 用于在屏幕上显示和管理翻译结果覆盖层
class TranslationOverlayManager {
  static final TranslationOverlayManager _instance = TranslationOverlayManager._internal();

  factory TranslationOverlayManager() {
    return _instance;
  }

  TranslationOverlayManager._internal();

  OverlayEntry? _overlayEntry;
  bool _isVisible = false;

  bool get isVisible => _isVisible;

  /// 显示翻译覆盖层
  void showTranslationOverlay(
    BuildContext context, {
    required String originalText,
    required String translatedText,
  }) {
    // 如果已经有覆盖层，先移除
    if (_isVisible) {
      removeOverlay();
    }

    // 获取Overlay状态
    final overlay = Overlay.of(context);

    // 创建覆盖层
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).size.height * 0.2, // 位于屏幕上方20%的位置
        left: 16,
        right: 16,
        child: TranslationOverlay(
          originalText: originalText,
          translatedText: translatedText,
          onClose: () {
            removeOverlay();
          },
        ),
      ),
    );

    // 添加覆盖层
    overlay.insert(_overlayEntry!);
    _isVisible = true;
  }

  /// 移除覆盖层
  void removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isVisible = false;
    }
  }

  /// 切换覆盖层显示状态
  void toggleOverlay(
    BuildContext context, {
    required String originalText,
    required String translatedText,
  }) {
    if (_isVisible) {
      removeOverlay();
    } else {
      showTranslationOverlay(
        context,
        originalText: originalText,
        translatedText: translatedText,
      );
    }
  }
}
