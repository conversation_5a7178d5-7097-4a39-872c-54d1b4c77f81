import 'package:flutter/material.dart';
import 'overlay_manager.dart';
import 'floating_action_widget.dart';

class AppFloatingWidget {
  static final AppFloatingWidget _instance = AppFloatingWidget._internal();
  final OverlayManager _overlayManager = OverlayManager();

  factory AppFloatingWidget() {
    return _instance;
  }

  AppFloatingWidget._internal();

  bool get isVisible => _overlayManager.isVisible;

  void show(
    BuildContext context, {
    Function()? onPressed,
    Widget icon = const Icon(Icons.touch_app, color: Colors.white),
    Color backgroundColor = Colors.blue,
    bool stickToEdge = true,
  }) {
    _overlayManager.showOverlay(
      context,
      FloatingActionWidget(
        onPressed: onPressed,
        icon: icon,
        backgroundColor: backgroundColor,
        stickToEdge: stickToEdge,
      ),
    );
  }

  void hide() {
    _overlayManager.removeOverlay();
  }

  void toggle(
    BuildContext context, {
    Function()? onPressed,
    Widget icon = const Icon(Icons.touch_app, color: Colors.white),
    Color backgroundColor = Colors.blue,
    bool stickToEdge = true,
  }) {
    _overlayManager.toggleOverlay(
      context,
      FloatingActionWidget(
        onPressed: onPressed,
        icon: icon,
        backgroundColor: backgroundColor,
        stickToEdge: stickToEdge,
      ),
    );
  }
}
