import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui' as ui;

/// 文本识别覆盖层组件
/// 用于在原文位置上显示识别的文本和翻译结果
class TextRecognitionOverlay extends StatefulWidget {
  final String originalText;
  final String translatedText;
  final Rect boundingBox;
  final VoidCallback? onClose;

  const TextRecognitionOverlay({
    Key? key,
    required this.originalText,
    required this.translatedText,
    required this.boundingBox,
    this.onClose,
  }) : super(key: key);

  @override
  State<TextRecognitionOverlay> createState() => _TextRecognitionOverlayState();
}

class _TextRecognitionOverlayState extends State<TextRecognitionOverlay> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: widget.boundingBox.left,
      top: widget.boundingBox.top,
      width: widget.boundingBox.width,
      height: widget.boundingBox.height,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: GestureDetector(
          onTap: () {
            // 点击覆盖层时显示详细信息
            _showDetailDialog(context);
          },
          child: Container(
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.2),
              border: Border.all(
                color: Colors.blue.withOpacity(0.5),
                width: 2,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Stack(
              children: [
                // 半透明背景，显示原文
                Positioned.fill(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(2),
                    child: BackdropFilter(
                      filter: ui.ImageFilter.blur(sigmaX: 1.5, sigmaY: 1.5),
                      child: Container(
                        color: Colors.white.withOpacity(0.7),
                        padding: const EdgeInsets.all(4),
                        child: Text(
                          widget.translatedText,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.black87,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 3,
                        ),
                      ),
                    ),
                  ),
                ),

                // 关闭按钮
                Positioned(
                  right: 0,
                  top: 0,
                  child: GestureDetector(
                    onTap: widget.onClose,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red.withOpacity(0.7),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        size: 12,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 显示详细信息对话框
  void _showDetailDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('识别结果'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '原文:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 4),
            Text(widget.originalText),
            const SizedBox(height: 12),
            const Text(
              '翻译:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 4),
            Text(widget.translatedText),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('关闭'),
          ),
          TextButton(
            onPressed: () {
              // 复制翻译结果到剪贴板
              _copyToClipboard(context, widget.translatedText);
              Navigator.of(context).pop();
            },
            child: const Text('复制翻译'),
          ),
        ],
      ),
    );
  }

  // 复制文本到剪贴板
  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已复制到剪贴板'),
        duration: Duration(seconds: 1),
      ),
    );
  }
}
