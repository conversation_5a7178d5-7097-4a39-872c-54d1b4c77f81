import 'package:flutter/material.dart';

class TextOverlay extends StatelessWidget {
  final String text;
  final Rect bounds;
  final VoidCallback? onTap;

  const TextOverlay({
    Key? key,
    required this.text,
    required this.bounds,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: bounds.left,
      top: bounds.top,
      width: bounds.width,
      height: bounds.height,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.1),
            border: Border.all(color: Colors.blue.withOpacity(0.5), width: 1),
          ),
          child: Center(
            child: Text(
              text,
              style: const TextStyle(color: Colors.black, fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}
