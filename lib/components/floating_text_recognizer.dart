import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';

class FloatingTextRecognizer extends StatefulWidget {
  final Function(String recognizedText, Rect bounds) onTextRecognized;
  final Widget child;

  const FloatingTextRecognizer({
    Key? key,
    required this.onTextRecognized,
    required this.child,
  }) : super(key: key);

  @override
  State<FloatingTextRecognizer> createState() => _FloatingTextRecognizerState();
}

class _FloatingTextRecognizerState extends State<FloatingTextRecognizer> {
  final TextRecognizer _textRecognizer = TextRecognizer();
  bool _isRecognizing = false;

  Future<void> _recognizeText(BuildContext context) async {
    if (_isRecognizing) return;

    setState(() {
      _isRecognizing = true;
    });

    try {
      // 获取屏幕截图
      RenderRepaintBoundary boundary =
          context.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData != null) {
        final InputImage inputImage = InputImage.fromBytes(
          bytes: byteData.buffer.asUint8List(),
          metadata: InputImageMetadata(
            size: Size(image.width.toDouble(), image.height.toDouble()),
            rotation: InputImageRotation.rotation0deg,
            format: InputImageFormat.bgra8888,
            bytesPerRow: image.width * 4,
          ),
        );

        final RecognizedText recognizedText = await _textRecognizer
            .processImage(inputImage);

        for (TextBlock block in recognizedText.blocks) {
          for (TextLine line in block.lines) {
            widget.onTextRecognized(line.text, line.boundingBox);
          }
        }
      }
    } catch (e) {
      debugPrint('Text recognition error: $e');
    } finally {
      setState(() {
        _isRecognizing = false;
      });
    }
  }

  @override
  void dispose() {
    _textRecognizer.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _recognizeText(context),
      child: RepaintBoundary(child: widget.child),
    );
  }
}
