import 'package:flutter/material.dart';

class OverlayManager {
  static final OverlayManager _instance = OverlayManager._internal();

  factory OverlayManager() {
    return _instance;
  }

  OverlayManager._internal();

  OverlayEntry? _overlayEntry;
  bool _isVisible = false;

  bool get isVisible => _isVisible;

  void showOverlay(BuildContext context, Widget widget) {
    if (_isVisible) {
      removeOverlay();
    }

    // Get the overlay state
    final overlay = Overlay.of(context);

    // Create overlay entry
    _overlayEntry = OverlayEntry(builder: (context) => widget);

    // Add overlay entry
    overlay.insert(_overlayEntry!);
    _isVisible = true;
  }

  void removeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isVisible = false;
    }
  }

  void toggleOverlay(BuildContext context, Widget widget) {
    if (_isVisible) {
      removeOverlay();
    } else {
      showOverlay(context, widget);
    }
  }
}
