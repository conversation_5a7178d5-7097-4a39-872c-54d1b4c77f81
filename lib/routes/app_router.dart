import 'package:go_router/go_router.dart';
import '../pages/main_page.dart';
import '../pages/login_page.dart';
import '../pages/floating_widget_demo_page.dart';
import '../pages/desktop_floating_demo_page.dart';
import '../pages/floating_translator_page.dart';
import '../pages/text_recognition_page.dart';

final router = GoRouter(
  initialLocation: '/',
  debugLogDiagnostics: true,
  routes: [
    GoRoute(
      path: '/',
      name: 'main',
      builder: (context, state) => const MainPage(),
    ),
    GoRoute(
      path: '/login',
      name: 'login',
      builder: (context, state) => const LoginPage(),
    ),
    GoRoute(
      path: '/floating-demo',
      name: 'floatingDemo',
      builder: (context, state) => const FloatingWidgetDemoPage(),
    ),
    GoRoute(
      path: '/desktop-floating-demo',
      name: 'desktopFloatingDemo',
      builder: (context, state) => const DesktopFloatingDemoPage(),
    ),
    GoRoute(
      path: '/floating-translator',
      name: 'floatingTranslator',
      builder: (context, state) => const FloatingTranslatorPage(),
    ),
    GoRoute(
      path: '/text-recognition',
      name: 'textRecognition',
      builder: (context, state) => const TextRecognitionPage(),
    ),
    // Add more routes as needed
  ],
);
