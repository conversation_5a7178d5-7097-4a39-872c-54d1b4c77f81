import 'package:mmkv/mmkv.dart';

enum KVMode { single, multi }

class KV {
  static late MMKV _instance;

  /// Initialize the KV storage
  static Future<void> init({
    required KVMode mode,
    required String cryptKey,
  }) async {
    // Initialize MMKV
    await MMKV.initialize();

    // Create instance based on mode
    if (mode == KVMode.multi) {
      _instance = MMKV.defaultMMKV(cryptKey: cryptKey);
    } else {
      _instance = MMKV.defaultMMKV(cryptKey: cryptKey);
    }
  }

  /// Get a value by key
  static T? get<T>(String key, {T? defaultValue}) {
    if (T == String) {
      return _instance.decodeString(key) as T?;
    } else if (T == int) {
      return _instance.decodeInt(key) as T?;
    } else if (T == bool) {
      return _instance.decodeBool(key) as T?;
    } else if (T == double) {
      return _instance.decodeDouble(key) as T?;
    }
    return defaultValue;
  }

  /// Save a value by key
  static bool set<T>(String key, T value) {
    if (value is String) {
      return _instance.encodeString(key, value);
    } else if (value is int) {
      return _instance.encodeInt(key, value);
    } else if (value is bool) {
      return _instance.encodeBool(key, value);
    } else if (value is double) {
      return _instance.encodeDouble(key, value);
    }
    return false;
  }

  /// Remove a value by key
  static void remove(String key) {
    _instance.removeValue(key);
  }

  /// Clear all values
  static void clear() {
    _instance.clearAll();
  }
}
