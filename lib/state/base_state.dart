import 'package:flutter/material.dart';

/// Abstract class that provides basic state management functionality.
abstract class BaseState extends ChangeNotifier {
  bool _isBusy = false;
  String? _error;

  /// Whether the state is busy loading data.
  bool get isBusy => _isBusy;

  /// The error message, if any.
  String? get error => _error;

  /// Whether there is an error.
  bool get isError => _error != null;

  /// The error message or an empty string.
  String get errorMessage => _error ?? '';

  /// Set the state to busy and clear any errors.
  void setBusy() {
    _isBusy = true;
    _error = null;
    notifyListeners();
  }

  /// Set the state to idle and clear any errors.
  void setIdle() {
    _isBusy = false;
    _error = null;
    notifyListeners();
  }

  /// Set an error message and set the state to idle.
  void setError(String message) {
    _isBusy = false;
    _error = message;
    notifyListeners();
  }
}
