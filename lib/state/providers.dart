import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import 'user_state.dart';

class AppStateProvider extends StatelessWidget {
  final Widget child;

  const AppStateProvider({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => UserState()),
        // Add other state providers here
      ],
      child: child,
    );
  }
}
