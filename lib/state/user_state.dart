import 'package:flutter/material.dart';
import '../network/models/user.dart';
import '../network/services/api_service.dart';
import 'base_state.dart';

class UserState extends BaseState {
  final ApiService _apiService;
  User? _currentUser;
  bool _isLoggedIn = false;
  String? _userId;
  String? _userName;
  String? _email;
  bool _isBusy = false;
  String? _error;

  UserState({ApiService? apiService})
    : _apiService = apiService ?? ApiService();

  User? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;
  String? get userId => _userId;
  String? get userName => _userName;
  String? get email => _email;
  bool get isBusy => _isBusy;
  String? get error => _error;
  bool get isError => _error != null;
  String get errorMessage => _error ?? '';

  void setBusy() {
    _isBusy = true;
    _error = null;
    notifyListeners();
  }

  void setIdle() {
    _isBusy = false;
    _error = null;
    notifyListeners();
  }

  void setError(String message) {
    _isBusy = false;
    _error = message;
    notifyListeners();
  }

  Future<bool> login(String username, String password) async {
    setBusy();
    try {
      // For demo purposes, we'll just simulate a successful login
      // Instead of making an actual API call
      // In a real app, you would use:
      // final response = await _apiService.login(username, password);

      await Future.delayed(const Duration(seconds: 1));

      _currentUser = User(
        id: '1',
        username: username,
        email: '$<EMAIL>',
      );
      _isLoggedIn = true;
      _userId = _currentUser!.id;
      _userName = _currentUser!.username;
      _email = _currentUser!.email;

      setIdle();
      return true;
    } catch (e) {
      setError(e.toString());
      return false;
    }
  }

  Future<void> logout() async {
    _currentUser = null;
    _isLoggedIn = false;
    _userId = null;
    _userName = null;
    _email = null;
    setIdle();
  }
}
