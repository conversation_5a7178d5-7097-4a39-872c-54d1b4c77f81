import '../http_client.dart';
import '../models/user.dart';
import '../models/base_response.dart';
import '../http_config.dart';

class ApiService {
  final HttpClient _httpClient = HttpClient();

  Future<BaseResponse<User>> login(String username, String password) async {
    try {
      final response = await _httpClient.post(
        HttpConfig.login,
        data: {'username': username, 'password': password},
      );

      return BaseResponse<User>.fromJson(
        response.data,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      return BaseResponse<User>.error('Login failed: $e');
    }
  }

  Future<BaseResponse<User>> getUserInfo(String userId) async {
    try {
      final response = await _httpClient.get('${HttpConfig.userInfo}/$userId');

      return BaseResponse<User>.fromJson(
        response.data,
        (json) => User.fromJson(json),
      );
    } catch (e) {
      return BaseResponse<User>.error('Failed to get user info: $e');
    }
  }
}
