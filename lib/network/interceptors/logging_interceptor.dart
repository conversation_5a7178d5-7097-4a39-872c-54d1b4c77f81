import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

class LoggingInterceptor extends Interceptor {
  final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 75,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    _logger.i('REQUEST[${options.method}] => PATH: ${options.path}');
    if (options.data != null) {
      _logger.d('REQUEST DATA => ${options.data}');
    }
    if (options.queryParameters.isNotEmpty) {
      _logger.d('REQUEST QUERY => ${options.queryParameters}');
    }
    _logger.d('REQUEST HEADERS => ${options.headers}');
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    _logger.i(
      'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}',
    );
    _logger.d('RESPONSE DATA => ${response.data}');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    _logger.e(
      'ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}',
    );
    if (err.response?.data != null) {
      _logger.e('ERROR DATA => ${err.response?.data}');
    }
    _logger.e('ERROR MESSAGE => ${err.message}');
    super.onError(err, handler);
  }
}
