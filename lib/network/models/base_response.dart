class BaseResponse<T> {
  final bool success;
  final String message;
  final T? data;

  BaseResponse({required this.success, required this.message, this.data});

  factory BaseResponse.success(T data, {String message = 'Success'}) {
    return BaseResponse<T>(success: true, message: message, data: data);
  }

  factory BaseResponse.error(String message) {
    return BaseResponse<T>(success: false, message: message, data: null);
  }

  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic> json) fromJsonT,
  ) {
    final success = json['success'] as bool? ?? false;
    final message = json['message'] as String? ?? '';

    T? data;
    if (success && json.containsKey('data')) {
      try {
        data = fromJsonT(json['data']);
      } catch (e) {
        print('Error parsing data: $e');
      }
    }

    return BaseResponse<T>(success: success, message: message, data: data);
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    final Map<String, dynamic> json = {'success': success, 'message': message};

    if (data != null) {
      json['data'] = toJsonT(data as T);
    }

    return json;
  }

  bool get isSuccess => success;
  bool get isError => !success;
}
